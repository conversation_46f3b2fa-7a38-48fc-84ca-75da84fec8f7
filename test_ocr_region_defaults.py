#!/usr/bin/env python3
"""
Test script to verify that all default OCR region values have been updated to 0.0, 66.9, 100, 32.9
"""

import sys
from pathlib import Path

def test_main_file_ocr_defaults():
    """Test that main file has updated OCR region defaults"""
    print("🧪 Testing Main File OCR Region Defaults...")
    print("=" * 50)
    
    try:
        # Read the main file
        main_file = Path("mffuhijack_main.py")
        if not main_file.exists():
            print("⚠️  Main file not found")
            return False
            
        content = main_file.read_text(encoding='utf-8')
        
        # Check for updated default values
        checks = [
            ("Y default value 66.9", 'ocr_region_y", 66.9,' in content),
            ("Height default value 32.9", 'ocr_region_height", 32.9,' in content),
            ("Default region tuple (66.9)", "return (0.0, 66.9, 100.0, 32.9)" in content),
            ("Initial region tuple (66.9)", "initial_region = (0.0, 66.9, 100.0, 32.9)" in content),
            ("No old 67.0 values", "67.0" not in content),
            ("No old 33.0 values", "33.0" not in content)
        ]
        
        passed = 0
        for desc, check in checks:
            if check:
                print(f"✅ {desc}: Found")
                passed += 1
            else:
                print(f"❌ {desc}: Not found")
        
        if passed >= 4:
            print(f"✅ Main file OCR defaults updated ({passed}/6 checks)")
            return True
        else:
            print(f"❌ Main file OCR defaults incomplete ({passed}/6 checks)")
            return False
            
    except Exception as e:
        print(f"❌ Error testing main file OCR defaults: {e}")
        return False

def test_live_scan_monitor_defaults():
    """Test that live scan monitor has updated OCR region defaults"""
    print("\n🧪 Testing Live Scan Monitor OCR Region Defaults...")
    print("=" * 50)
    
    try:
        # Read the live scan monitor file
        monitor_file = Path("MFFUHijack_Support/live_scan_monitor.py")
        if not monitor_file.exists():
            print("⚠️  Live scan monitor file not found")
            return False
            
        content = monitor_file.read_text(encoding='utf-8')
        
        # Check for updated default values
        checks = [
            ("Y default value 66.9", 'ocr_region_y", 66.9,' in content),
            ("Height default value 32.9", 'ocr_region_height", 32.9,' in content),
            ("Default region tuple (66.9)", "return (0.0, 66.9, 100.0, 32.9)" in content),
            ("No old 67.0 values", "67.0" not in content),
            ("No old 33.0 values", "33.0" not in content)
        ]
        
        passed = 0
        for desc, check in checks:
            if check:
                print(f"✅ {desc}: Found")
                passed += 1
            else:
                print(f"❌ {desc}: Not found")
        
        if passed >= 3:
            print(f"✅ Live scan monitor OCR defaults updated ({passed}/5 checks)")
            return True
        else:
            print(f"❌ Live scan monitor OCR defaults incomplete ({passed}/5 checks)")
            return False
            
    except Exception as e:
        print(f"❌ Error testing live scan monitor OCR defaults: {e}")
        return False

def test_manual_ocr_region_selector_defaults():
    """Test that manual OCR region selector has updated defaults"""
    print("\n🧪 Testing Manual OCR Region Selector Defaults...")
    print("=" * 50)
    
    try:
        # Read the manual OCR region selector file
        selector_file = Path("MFFUHijack_Support/manual_ocr_region_selector.py")
        if not selector_file.exists():
            print("⚠️  Manual OCR region selector file not found")
            return False
            
        content = selector_file.read_text(encoding='utf-8')
        
        # Check for updated default values
        checks = [
            ("Y position 0.669", "* 0.669" in content),
            ("Height 0.329", "* 0.329" in content),
            ("Preset button 66.9", "apply_preset(0, 66.9, 100, 32.9)" in content),
            ("Test region 66.9", "initial_region = (0.0, 66.9, 100.0, 32.9)" in content),
            ("No old 0.67 values", "0.67" not in content),
            ("No old 0.25 values", "0.25" not in content)
        ]
        
        passed = 0
        for desc, check in checks:
            if check:
                print(f"✅ {desc}: Found")
                passed += 1
            else:
                print(f"❌ {desc}: Not found")
        
        if passed >= 4:
            print(f"✅ Manual OCR region selector defaults updated ({passed}/6 checks)")
            return True
        else:
            print(f"❌ Manual OCR region selector defaults incomplete ({passed}/6 checks)")
            return False
            
    except Exception as e:
        print(f"❌ Error testing manual OCR region selector defaults: {e}")
        return False

def test_region_selection_guide_defaults():
    """Test that region selection guide documentation is updated"""
    print("\n🧪 Testing Region Selection Guide Documentation...")
    print("=" * 50)
    
    try:
        # Read the region selection guide file
        guide_file = Path("MFFUHijack_Support/REGION_SELECTION_GUIDE.md")
        if not guide_file.exists():
            print("⚠️  Region selection guide file not found")
            return False
            
        content = guide_file.read_text(encoding='utf-8')
        
        # Check for updated documentation
        checks = [
            ("Bottom Third 66.9%", "66.9%" in content),
            ("Bottom Third 32.9%", "32.9%" in content),
            ("No old 67% values", "67%" not in content),
            ("No old 33% values", "33%" not in content)
        ]
        
        passed = 0
        for desc, check in checks:
            if check:
                print(f"✅ {desc}: Found")
                passed += 1
            else:
                print(f"❌ {desc}: Not found")
        
        if passed >= 2:
            print(f"✅ Region selection guide documentation updated ({passed}/4 checks)")
            return True
        else:
            print(f"❌ Region selection guide documentation incomplete ({passed}/4 checks)")
            return False
            
    except Exception as e:
        print(f"❌ Error testing region selection guide documentation: {e}")
        return False

def test_new_default_region_values():
    """Test the new default region values"""
    print("\n🧪 Testing New Default Region Values...")
    print("=" * 50)
    
    # Document the new default region
    new_region = (0.0, 66.9, 100.0, 32.9)
    x, y, w, h = new_region
    
    print("📝 New Default OCR Region:")
    print(f"   X Position: {x}% (left edge)")
    print(f"   Y Position: {y}% (66.9% from top)")
    print(f"   Width: {w}% (full width)")
    print(f"   Height: {h}% (32.9% height)")
    
    # Calculate coverage area
    coverage_start = y
    coverage_end = y + h
    
    print(f"\n📊 Coverage Area:")
    print(f"   Covers: {coverage_start}% to {coverage_end}% of screen height")
    print(f"   Covers: 0% to 100% of screen width")
    
    # Compare with old values
    old_region = (0.0, 67.0, 100.0, 33.0)
    old_x, old_y, old_w, old_h = old_region
    
    print(f"\n🔄 Changes from Old Default:")
    print(f"   Y Position: {old_y}% → {y}% (moved up by {old_y - y}%)")
    print(f"   Height: {old_h}% → {h}% (reduced by {old_h - h}%)")
    print(f"   X Position: {old_x}% → {x}% (no change)")
    print(f"   Width: {old_w}% → {w}% (no change)")
    
    print(f"\n✅ New default region values documented")
    return True

def main():
    """Run all OCR region default tests"""
    print("🚀 OCR Region Default Values Test")
    print("=" * 60)
    
    tests = [
        ("Main File OCR Defaults", test_main_file_ocr_defaults),
        ("Live Scan Monitor Defaults", test_live_scan_monitor_defaults),
        ("Manual OCR Region Selector Defaults", test_manual_ocr_region_selector_defaults),
        ("Region Selection Guide Documentation", test_region_selection_guide_defaults),
        ("New Default Region Values", test_new_default_region_values)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}: EXCEPTION - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All OCR region defaults successfully updated!")
        print("\n✅ Updated Default Region:")
        print("   • X: 0.0% (left edge)")
        print("   • Y: 66.9% (slightly higher than before)")
        print("   • Width: 100% (full width)")
        print("   • Height: 32.9% (slightly smaller than before)")
        print("\n🎯 The default OCR region is now set to 0.0, 66.9, 100, 32.9 as requested!")
        return True
    else:
        print("⚠️  Some defaults may not be fully updated. Check the output above.")
        return False

if __name__ == "__main__":
    main()
