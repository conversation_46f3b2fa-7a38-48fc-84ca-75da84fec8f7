#!/usr/bin/env python3
"""
Test script to verify that code extraction prioritizes "CODE:" pattern
"""

import sys
import re
from pathlib import Path

def test_code_pattern_priority():
    """Test that CODE: pattern is prioritized in main file"""
    print("🧪 Testing CODE: Pattern Priority in Main File...")
    print("=" * 50)
    
    try:
        # Read the main file
        main_file = Path("mffuhijack_main.py")
        if not main_file.exists():
            print("⚠️  Main file not found")
            return False
            
        content = main_file.read_text(encoding='utf-8')
        
        # Check that CODE: pattern is first in the patterns list
        patterns_section = content.split("patterns = [")[1].split("]")[0]
        
        # Check for prioritized patterns
        checks = [
            ("CODE: pattern first", "r'CODE:" in patterns_section.split('\n')[1]),
            ("USE CODE pattern second", "r'USE\\s+CODE" in patterns_section.split('\n')[2]),
            ("High priority comment", "# PRIMARY:" in patterns_section),
            ("Early return logic", "if i <= 1:" in content),
            ("Priority logging", "Using high-priority pattern" in content)
        ]
        
        passed = 0
        for desc, check in checks:
            if check:
                print(f"✅ {desc}: Found")
                passed += 1
            else:
                print(f"❌ {desc}: Not found")
        
        if passed >= 4:
            print(f"✅ CODE: pattern priority implemented ({passed}/5 checks)")
            return True
        else:
            print(f"❌ CODE: pattern priority incomplete ({passed}/5 checks)")
            return False
            
    except Exception as e:
        print(f"❌ Error testing CODE: pattern priority: {e}")
        return False

def test_livestream_gui_pattern_priority():
    """Test that CODE: pattern is prioritized in livestream GUI"""
    print("\n🧪 Testing CODE: Pattern Priority in Livestream GUI...")
    print("=" * 50)
    
    try:
        # Read the livestream GUI file
        gui_file = Path("MFFUHijack_Support/livestream_testing_gui.py")
        if not gui_file.exists():
            print("⚠️  Livestream GUI file not found")
            return False
            
        content = gui_file.read_text(encoding='utf-8')
        
        # Check for prioritized patterns
        checks = [
            ("CODE: pattern first", "r'CODE:" in content and "# PRIMARY:" in content),
            ("USE CODE pattern second", "r'USE\\s+CODE" in content and "# SECONDARY:" in content),
            ("Account type pattern third", "# TERTIARY:" in content),
            ("Fallback pattern last", "# FALLBACK:" in content),
            ("Priority logging", "Found code using" in content and "pattern:" in content),
            ("Early return logic", "if i <= 1:" in content)
        ]
        
        passed = 0
        for desc, check in checks:
            if check:
                print(f"✅ {desc}: Found")
                passed += 1
            else:
                print(f"❌ {desc}: Not found")
        
        if passed >= 4:
            print(f"✅ Livestream GUI CODE: priority implemented ({passed}/6 checks)")
            return True
        else:
            print(f"❌ Livestream GUI CODE: priority incomplete ({passed}/6 checks)")
            return False
            
    except Exception as e:
        print(f"❌ Error testing livestream GUI pattern priority: {e}")
        return False

def test_ocr_utils_code_extraction():
    """Test that OCR utils already has proper CODE: extraction"""
    print("\n🧪 Testing OCR Utils CODE: Extraction...")
    print("=" * 50)
    
    try:
        # Read the OCR utils file
        ocr_file = Path("MFFUHijack_Support/ocr_utils.py")
        if not ocr_file.exists():
            print("⚠️  OCR utils file not found")
            return False
            
        content = ocr_file.read_text(encoding='utf-8')
        
        # Check for proper CODE: extraction
        checks = [
            ("extract_code_after_keyword method", "def extract_code_after_keyword" in content),
            ("CODE: pattern regex", "r'CODE:\\s*([A-Z0-9\\-_]+)'" in content),
            ("Method 1 comment", "# Method 1: Look for \"CODE:\"" in content),
            ("Preferred comment", "(preferred)" in content),
            ("S/5 confusion correction", "correct_s5_confusion" in content),
            ("Two-step logic", "two-step logic" in content)
        ]
        
        passed = 0
        for desc, check in checks:
            if check:
                print(f"✅ {desc}: Found")
                passed += 1
            else:
                print(f"❌ {desc}: Not found")
        
        if passed >= 5:
            print(f"✅ OCR utils CODE: extraction properly implemented ({passed}/6 checks)")
            return True
        else:
            print(f"❌ OCR utils CODE: extraction incomplete ({passed}/6 checks)")
            return False
            
    except Exception as e:
        print(f"❌ Error testing OCR utils CODE: extraction: {e}")
        return False

def test_code_extraction_examples():
    """Test code extraction with example text"""
    print("\n🧪 Testing Code Extraction Examples...")
    print("=" * 50)
    
    # Test examples that should prioritize CODE: pattern
    test_cases = [
        {
            "text": "x5 FREE RESETS USE CODE: RESET3J",
            "expected_code": "RESET3J",
            "expected_pattern": "CODE:"
        },
        {
            "text": "STARTER ACCOUNTS USE CODE: START123",
            "expected_code": "START123", 
            "expected_pattern": "CODE:"
        },
        {
            "text": "Get your EXPERT account with CODE: EXPERT99",
            "expected_code": "EXPERT99",
            "expected_pattern": "CODE:"
        },
        {
            "text": "STARTER PLUS: PLUS456 and also CODE: REALCODE",
            "expected_code": "REALCODE",  # Should pick CODE: over account type pattern
            "expected_pattern": "CODE:"
        }
    ]
    
    # Simple regex test to simulate the pattern matching
    patterns = [
        (r'CODE:\s*([A-Z0-9]{4,10})', "CODE:"),
        (r'USE\s+CODE\s*:?\s*([A-Z0-9]{4,10})', "USE CODE"),
        (r'(?:RESET|STARTER|EXPERT|PLUS):\s*([A-Z0-9]{4,10})', "ACCOUNT TYPE:"),
        (r'(?:RESET|START|PLUS)(\d+[A-Z]*)', "SPECIFIC FORMAT"),
        (r'\b([A-Z0-9]{6,8})\b', "FALLBACK")
    ]
    
    passed = 0
    for i, test_case in enumerate(test_cases):
        text = test_case["text"].upper()
        expected_code = test_case["expected_code"]
        expected_pattern = test_case["expected_pattern"]
        
        print(f"\n   Test {i+1}: '{test_case['text']}'")
        
        found_code = None
        found_pattern = None
        
        # Test pattern matching in priority order
        for pattern, pattern_name in patterns:
            match = re.search(pattern, text)
            if match:
                found_code = match.group(1)
                found_pattern = pattern_name
                print(f"      Found: '{found_code}' using {pattern_name} pattern")
                
                # Stop at high-priority patterns
                if pattern_name in ["CODE:", "USE CODE"]:
                    break
                break
        
        if found_code == expected_code and found_pattern == expected_pattern:
            print(f"   ✅ PASS: Correctly found '{found_code}' using {found_pattern}")
            passed += 1
        else:
            print(f"   ❌ FAIL: Expected '{expected_code}' ({expected_pattern}), got '{found_code}' ({found_pattern})")
    
    if passed == len(test_cases):
        print(f"\n✅ All code extraction examples passed ({passed}/{len(test_cases)})")
        return True
    else:
        print(f"\n❌ Some code extraction examples failed ({passed}/{len(test_cases)})")
        return False

def main():
    """Run all CODE: pattern priority tests"""
    print("🚀 CODE: Pattern Priority Test")
    print("=" * 60)
    
    tests = [
        ("Main File Pattern Priority", test_code_pattern_priority),
        ("Livestream GUI Pattern Priority", test_livestream_gui_pattern_priority),
        ("OCR Utils CODE: Extraction", test_ocr_utils_code_extraction),
        ("Code Extraction Examples", test_code_extraction_examples)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}: EXCEPTION - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All CODE: pattern priority tests passed!")
        print("\n✅ Implemented Features:")
        print("   • CODE: pattern has highest priority in all extraction methods")
        print("   • Early return logic prevents fallback pattern false matches")
        print("   • Proper logging shows which pattern was used")
        print("   • Consistent implementation across main file and GUI")
        print("   • OCR utils already has robust CODE: extraction")
        print("\n🎯 Code extraction now prioritizes text after 'CODE:' as requested!")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    main()
