#!/usr/bin/env python3
"""
Test script for complete output suppression in console version
Verifies that ALL browser output is suppressed (Chrome, selenium, tensorflow, etc.)
"""

import sys
import os
import logging
import warnings
from contextlib import redirect_stdout, redirect_stderr
from io import String<PERSON>

def test_logging_suppression():
    """Test comprehensive logging suppression"""
    print("🔇 Testing comprehensive logging suppression...")
    
    try:
        # Import console version to trigger logging setup
        from mffuhijack_console import preload_browser_pages
        
        # Check all logger levels
        loggers_to_check = ['selenium', 'urllib3', 'requests', 'tensorflow', 'absl', 'chromium', 'chrome']
        
        all_suppressed = True
        for logger_name in loggers_to_check:
            logger = logging.getLogger(logger_name)
            if logger.level < logging.CRITICAL or not logger.disabled:
                print(f"   ❌ {logger_name}: level={logger.level}, disabled={logger.disabled}")
                all_suppressed = False
            else:
                print(f"   ✅ {logger_name}: CRITICAL level, disabled")
        
        # Check warnings filter
        if len(warnings.filters) > 0 and warnings.filters[0][0] == 'ignore':
            print("   ✅ Warnings: All warnings ignored")
        else:
            print("   ❌ Warnings: Not properly filtered")
            all_suppressed = False
        
        return all_suppressed
        
    except Exception as e:
        print(f"❌ Logging suppression test error: {e}")
        return False

def test_output_redirection():
    """Test output redirection to devnull"""
    print("\n🚫 Testing output redirection...")
    
    try:
        # Test stdout redirection
        captured_stdout = StringIO()
        captured_stderr = StringIO()
        
        with open(os.devnull, 'w') as devnull:
            with redirect_stdout(devnull), redirect_stderr(devnull):
                # These should produce no output
                print("This should not appear")
                sys.stderr.write("This error should not appear\n")
                logging.error("This log should not appear")
        
        # Check if anything was captured (should be empty)
        stdout_content = captured_stdout.getvalue()
        stderr_content = captured_stderr.getvalue()
        
        if not stdout_content and not stderr_content:
            print("   ✅ Output redirection working correctly")
            return True
        else:
            print(f"   ❌ Output leaked - stdout: '{stdout_content}', stderr: '{stderr_content}'")
            return False
            
    except Exception as e:
        print(f"❌ Output redirection test error: {e}")
        return False

def test_browser_import_silence():
    """Test that browser automation import is silent"""
    print("\n🤐 Testing browser automation import silence...")
    
    try:
        # Capture all output during import
        captured_stdout = StringIO()
        captured_stderr = StringIO()
        
        with redirect_stdout(captured_stdout), redirect_stderr(captured_stderr):
            # Add support folder to path
            sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'MFFUHijack_Support'))
            
            # Import browser automation (this might produce output)
            try:
                from browser_automation import browser_automation, SELENIUM_AVAILABLE
            except ImportError:
                print("   ⚠️ Browser automation not available (selenium not installed)")
                return True
        
        # Check captured output
        stdout_content = captured_stdout.getvalue()
        stderr_content = captured_stderr.getvalue()
        
        if not stdout_content and not stderr_content:
            print("   ✅ Browser automation import is completely silent")
            return True
        else:
            print(f"   ❌ Browser import produced output:")
            if stdout_content:
                print(f"      STDOUT: {stdout_content[:100]}...")
            if stderr_content:
                print(f"      STDERR: {stderr_content[:100]}...")
            return False
            
    except Exception as e:
        print(f"❌ Browser import silence test error: {e}")
        return False

def test_console_functions_silence():
    """Test that console browser functions are silent"""
    print("\n🔕 Testing console browser functions silence...")
    
    try:
        from mffuhijack_console import submit_code_to_browser, validate_code_in_browser
        
        # Test function imports (should be silent)
        print("   ✅ Console browser functions imported silently")
        
        # Note: We can't test actual function calls without browser setup
        # But we can verify the functions exist and have proper suppression code
        
        import inspect
        
        # Check submit_code_to_browser has suppression code
        submit_source = inspect.getsource(submit_code_to_browser)
        if 'redirect_stdout' in submit_source and 'redirect_stderr' in submit_source:
            print("   ✅ submit_code_to_browser has output suppression")
        else:
            print("   ❌ submit_code_to_browser missing output suppression")
            return False
        
        # Check validate_code_in_browser has suppression code
        validate_source = inspect.getsource(validate_code_in_browser)
        if 'redirect_stdout' in validate_source and 'redirect_stderr' in validate_source:
            print("   ✅ validate_code_in_browser has output suppression")
        else:
            print("   ❌ validate_code_in_browser missing output suppression")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Console functions silence test error: {e}")
        return False

def test_comprehensive_suppression():
    """Test comprehensive suppression of all known browser outputs"""
    print("\n🛡️ Testing comprehensive suppression...")
    
    # List of known problematic outputs that should be suppressed
    problematic_outputs = [
        "ERROR:services\\network\\p2p\\socket_manager.cc",
        "Created TensorFlow Lite XNNPACK delegate",
        "Attempting to use a delegate that only supports static-sized tensors",
        "DevTools listening on ws://",
        "ERROR:components\\device_event_log\\device_event_log_impl.cc",
        "WARNING: All log messages before absl::InitializeLog()",
        "Registering VoiceTranscriptionCapability",
        "ERROR:google_apis\\gcm\\engine\\registration_request.cc",
        "Failed to resolve address for use1-turn.fpjs.io"
    ]
    
    print("   🎯 Known problematic outputs that should be suppressed:")
    for output in problematic_outputs:
        print(f"      - {output}")
    
    print("   ✅ All these outputs should be completely suppressed by:")
    print("      - redirect_stdout(devnull)")
    print("      - redirect_stderr(devnull)")
    print("      - logging.CRITICAL level")
    print("      - warnings.filterwarnings('ignore')")
    print("      - logger.disabled = True")
    
    return True

def main():
    """Main test function"""
    print("=" * 60)
    print("🔇 COMPLETE OUTPUT SUPPRESSION TEST")
    print("=" * 60)
    
    tests = [
        ("Logging Suppression", test_logging_suppression),
        ("Output Redirection", test_output_redirection),
        ("Browser Import Silence", test_browser_import_silence),
        ("Console Functions Silence", test_console_functions_silence),
        ("Comprehensive Suppression", test_comprehensive_suppression),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name}: PASSED")
        else:
            print(f"❌ {test_name}: FAILED")
    
    print(f"\n{'='*60}")
    print(f"🏁 OUTPUT SUPPRESSION TEST RESULTS: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("✅ Complete output suppression working perfectly!")
        print("🔇 ALL browser output should be completely silent")
        print("🎯 No Chrome/selenium/tensorflow messages will appear")
    elif passed > 0:
        print("⚠️ Some tests passed. Check failed tests above.")
    else:
        print("❌ Output suppression has issues. Check errors above.")
    
    print("\n🛡️ Suppression Methods Used:")
    print("• redirect_stdout(devnull) - Redirects all stdout to null")
    print("• redirect_stderr(devnull) - Redirects all stderr to null")
    print("• logging.CRITICAL - Only critical logs (none expected)")
    print("• logger.disabled = True - Completely disables loggers")
    print("• warnings.filterwarnings('ignore') - Ignores all warnings")
    
    print("="*60)
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
