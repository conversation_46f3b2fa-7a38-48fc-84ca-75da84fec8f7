LSTMEVAL(1)
===========
:doctype: manpage

NAME
----
lstmeval - Evaluation program for LSTM-based networks.

SYNOPSIS
--------
*lstmeval* --model 'lang.lstm|modelname_checkpoint|modelname_N.NN_NN_NN.checkpoint' [--traineddata lang/lang.traineddata] --eval_listfile 'lang.eval_files.txt' [--verbosity N] [--max_image_MB NNNN]

DESCRIPTION
-----------
lstmeval(1) evaluates LSTM-based networks. Either a recognition model or a training checkpoint can be given as input for evaluation along with a list of lstmf files. If evaluating a training checkpoint, '--traineddata' should also be specified. Intermediate training checkpoints can also be used.

OPTIONS
-------
'--model  FILE'::
  Name of model file (training or recognition)  (type:string default:)

'--traineddata  FILE'::
  If model is a training checkpoint, then traineddata must be the traineddata file that was given to the trainer  (type:string default:)

'--eval_listfile  FILE'::
  File listing sample files in lstmf training format.  (type:string default:)

'--max_image_MB  INT'::
  Max memory to use for images.  (type:int default:2000)

'--verbosity  INT'::
  Amount of diagnosting information to output (0-2).  (type:int default:1)

HISTORY
-------
lstmeval(1) was first made available for tesseract4.00.00alpha.

RESOURCES
---------
Main web site: <https://github.com/tesseract-ocr> +
Information on training tesseract LSTM: <https://tesseract-ocr.github.io/tessdoc/TrainingTesseract-4.00.html>

SEE ALSO
--------
tesseract(1)

COPYING
-------
Copyright \(C) 2012 Google, Inc.
Licensed under the Apache License, Version 2.0

AUTHOR
------
The Tesseract OCR engine was written by Ray Smith and his research groups
at Hewlett Packard (1985-1995) and Google (2006-2018).
