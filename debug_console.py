#!/usr/bin/env python3
"""
Debug script to test console functionality
"""

print("=== DEBUG CONSOLE TEST ===")
print("1. Basic print working")

try:
    import os
    print("2. OS import working")
except Exception as e:
    print(f"2. OS import failed: {e}")

try:
    import sys
    print("3. Sys import working")
except Exception as e:
    print(f"3. Sys import failed: {e}")

try:
    from colorama import init, Fore, Back, Style
    init(autoreset=True)
    print("4. Colorama import working")
    print(Fore.CYAN + "5. Colorama colors working" + Style.RESET_ALL)
except Exception as e:
    print(f"4. Colorama import failed: {e}")

try:
    sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'MFFUHijack_Support'))
    print("6. Path append working")
except Exception as e:
    print(f"6. Path append failed: {e}")

try:
    from console_ocr import console_ocr, is_ocr_available
    print("7. Console OCR import working")
except Exception as e:
    print(f"7. Console OCR import failed: {e}")

try:
    import cv2
    print("8. OpenCV import working")
except Exception as e:
    print(f"8. OpenCV import failed: {e}")

try:
    import yt_dlp
    print("9. yt-dlp import working")
except Exception as e:
    print(f"9. yt-dlp import failed: {e}")

print("=== TESTING CONSOLE INTERFACE ===")

try:
    from mffuhijack_console import Colors, ConsoleInterface
    print("10. MFFUHijack imports working")
    
    console = ConsoleInterface()
    print("11. ConsoleInterface created")
    
    print("12. Testing colors:")
    print(Colors.colorize("Sky blue test", Colors.SKY_BLUE))
    print(Colors.colorize("Light gray test", Colors.LIGHT_GRAY))
    
    print("13. About to show banner...")
    console.show_banner()
    print("14. Banner shown successfully")
    
except Exception as e:
    print(f"Console interface test failed: {e}")
    import traceback
    traceback.print_exc()

print("=== DEBUG TEST COMPLETE ===")
