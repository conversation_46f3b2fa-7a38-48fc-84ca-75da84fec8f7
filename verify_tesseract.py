#!/usr/bin/env python3
"""
Simple verification script for Tesseract OCR availability
"""

import sys
import os

def check_pytesseract():
    """Check if pytesseract is available"""
    try:
        import pytesseract
        print("✅ pytesseract package is available")
        return True
    except ImportError:
        print("❌ pytesseract package not installed")
        return False

def check_tesseract_binary():
    """Check if Tesseract binary is available"""
    try:
        import pytesseract
        import platform
        
        # Set path for Windows
        if platform.system() == "Windows":
            tesseract_paths = [
                r"C:\Program Files\Tesseract-OCR\tesseract.exe",
                r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
            ]
            
            for path in tesseract_paths:
                if os.path.exists(path):
                    pytesseract.pytesseract.tesseract_cmd = path
                    print(f"✅ Found Tesseract at: {path}")
                    break
        
        # Test Tesseract
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract version: {version}")
        return True
        
    except Exception as e:
        print(f"❌ Tesseract binary issue: {e}")
        return False

def check_availability_flags():
    """Check the availability flags in main application"""
    try:
        # Add support folder to path
        sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'MFFUHijack_Support'))
        
        # Import the main file to check flags
        import importlib.util
        spec = importlib.util.spec_from_file_location("main", "mffuhijack_main.py")
        main_module = importlib.util.module_from_spec(spec)
        
        # Just check the imports without executing the full module
        print("Checking availability flags...")
        
        # Check pytesseract import
        try:
            import pytesseract
            print("✅ TESSERACT_AVAILABLE should be True")
        except ImportError:
            print("❌ TESSERACT_AVAILABLE should be False")
            
        # Check easyocr import  
        try:
            import easyocr
            print("✅ EASYOCR_AVAILABLE should be True")
        except ImportError:
            print("❌ EASYOCR_AVAILABLE should be False")
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking availability flags: {e}")
        return False

def main():
    """Main verification function"""
    print("=" * 50)
    print("🔍 TESSERACT VERIFICATION")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: pytesseract package
    if check_pytesseract():
        tests_passed += 1
    
    # Test 2: Tesseract binary
    if check_tesseract_binary():
        tests_passed += 1
        
    # Test 3: Availability flags
    if check_availability_flags():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"🏁 VERIFICATION RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ Tesseract should appear as an OCR option!")
        print("🎯 Check the OCR Engine dropdown in the application.")
    else:
        print("❌ Some issues found. Check the errors above.")
    
    print("=" * 50)
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
