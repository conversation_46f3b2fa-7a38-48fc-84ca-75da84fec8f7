#!/usr/bin/env python3
"""
Test script for GUI credentials integration
Tests loading credentials from both GUI and console versions
"""

import sys
import os
import json

def test_gui_credentials_loading():
    """Test loading credentials from GUI version"""
    print("🔍 Testing GUI credentials integration...")
    
    try:
        from mffuhijack_console import CredentialsManager
        
        # Test 1: Check if PyQt6 is available
        print("\n1. Testing PyQt6 availability:")
        try:
            from PyQt6.QtCore import QSettings, QCoreApplication
            print("   ✅ PyQt6 available - can read GUI credentials")
            pyqt_available = True
        except ImportError:
            print("   ❌ PyQt6 not available - GUI credentials will be skipped")
            pyqt_available = False
        
        # Test 2: Create credentials manager
        print("\n2. Testing CredentialsManager:")
        creds = CredentialsManager()
        print("   ✅ CredentialsManager created successfully")
        
        # Test 3: Check current credentials
        print("\n3. Testing current credentials:")
        has_creds = creds.has_credentials()
        if has_creds:
            username, password = creds.get_credentials()
            source = creds.get_credentials_source()
            print(f"   ✅ Found credentials: {username} (from {source} version)")
        else:
            print("   📝 No credentials found")
        
        # Test 4: Simulate GUI credentials (if PyQt6 available)
        if pyqt_available:
            print("\n4. Testing GUI credentials simulation:")
            try:
                # Set up QApplication context
                from PyQt6.QtCore import QSettings, QCoreApplication
                
                # Create a temporary QSettings to simulate GUI credentials
                QCoreApplication.setApplicationName("MFFUHijack")
                QCoreApplication.setOrganizationName("jordan")
                
                settings = QSettings()
                
                # Check if GUI credentials already exist
                existing_username = settings.value("browser/username", "", type=str)
                if existing_username:
                    print(f"   ✅ Found existing GUI credentials: {existing_username}")
                else:
                    print("   📝 No existing GUI credentials found")
                    
                    # Simulate setting GUI credentials for testing
                    print("   🧪 Simulating GUI credentials for test...")
                    settings.setValue("browser/username", "gui_test_user")
                    settings.setValue("browser/password", "gui_test_pass")
                    settings.sync()
                    print("   ✅ Test GUI credentials set")
                
                # Test loading with new credentials manager
                print("   🔄 Testing reload with GUI credentials...")
                new_creds = CredentialsManager()
                if new_creds.has_credentials():
                    username, password = new_creds.get_credentials()
                    source = new_creds.get_credentials_source()
                    print(f"   ✅ Loaded credentials: {username} (from {source} version)")
                else:
                    print("   ❌ Failed to load GUI credentials")
                
                # Clean up test credentials if we created them
                if not existing_username:
                    settings.remove("browser/username")
                    settings.remove("browser/password")
                    settings.sync()
                    print("   🧹 Test GUI credentials cleaned up")
                
            except Exception as e:
                print(f"   ❌ GUI credentials test error: {e}")
        
        # Test 5: Console credentials priority
        print("\n5. Testing console credentials priority:")
        
        # Save console credentials
        creds.set_credentials("console_test_user", "console_test_pass")
        print("   ✅ Console credentials saved")
        
        # Create new instance to test priority
        priority_creds = CredentialsManager()
        if priority_creds.has_credentials():
            username, password = priority_creds.get_credentials()
            source = priority_creds.get_credentials_source()
            print(f"   ✅ Priority test: {username} (from {source} version)")
            
            if source == "Console":
                print("   ✅ Console credentials correctly take priority")
            else:
                print("   ⚠️ Expected console credentials to take priority")
        
        # Clean up test console credentials
        priority_creds.clear_credentials()
        print("   🧹 Test console credentials cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI credentials test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_credentials_workflow():
    """Test the complete credentials workflow"""
    print("\n" + "=" * 60)
    print("🔄 TESTING COMPLETE CREDENTIALS WORKFLOW")
    print("=" * 60)
    
    try:
        from mffuhijack_console import CredentialsManager
        
        # Start fresh
        creds = CredentialsManager()
        
        print("\n1. Initial state:")
        if creds.has_credentials():
            username, password = creds.get_credentials()
            source = creds.get_credentials_source()
            print(f"   Found: {username} (from {source})")
        else:
            print("   No credentials found")
        
        print("\n2. Setting console credentials:")
        creds.set_credentials("workflow_user", "workflow_pass")
        username, password = creds.get_credentials()
        source = creds.get_credentials_source()
        print(f"   Set: {username} (source: {source})")
        
        print("\n3. Testing persistence:")
        new_creds = CredentialsManager()
        if new_creds.has_credentials():
            username, password = new_creds.get_credentials()
            source = new_creds.get_credentials_source()
            print(f"   Loaded: {username} (from {source})")
        else:
            print("   ❌ Credentials not persisted")
        
        print("\n4. Clearing credentials:")
        new_creds.clear_credentials()
        if new_creds.has_credentials():
            username, password = new_creds.get_credentials()
            source = new_creds.get_credentials_source()
            print(f"   Still found: {username} (from {source})")
        else:
            print("   ✅ Credentials cleared successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Workflow test failed: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("🧪 GUI CREDENTIALS INTEGRATION TEST")
    print("=" * 60)
    
    tests = [
        ("GUI Credentials Loading", test_gui_credentials_loading),
        ("Complete Workflow", test_credentials_workflow),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name}: PASSED")
        else:
            print(f"❌ {test_name}: FAILED")
    
    print(f"\n{'='*60}")
    print(f"🏁 GUI INTEGRATION TEST RESULTS: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("✅ GUI credentials integration working perfectly!")
        print("🎯 Console version can now use GUI credentials automatically")
    elif passed > 0:
        print("⚠️ Some tests passed. Check failed tests above.")
    else:
        print("❌ GUI integration has issues. Check errors above.")
    
    print("\n🎯 How it works:")
    print("• Console checks GUI version credentials first (QSettings)")
    print("• Console credentials override GUI credentials if both exist")
    print("• Users see source information: 'from GUI version' or 'from Console version'")
    print("• Clearing only removes console credentials, leaves GUI intact")
    
    print("="*60)
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
