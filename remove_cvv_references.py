#!/usr/bin/env python3
"""
Script to remove all CVV references from the main file
"""

import re
from pathlib import Path

def remove_cvv_references():
    """Remove all CVV-related code from the main file"""
    main_file = Path("mffuhijack_main.py")
    if not main_file.exists():
        print("❌ Main file not found")
        return False
    
    content = main_file.read_text(encoding='utf-8')
    original_content = content
    
    # Remove CVV-related lines
    lines = content.split('\n')
    filtered_lines = []
    
    skip_next = False
    for i, line in enumerate(lines):
        # Skip lines that contain CVV references
        if any(cvv_term in line.lower() for cvv_term in ['cvv', 'card cvv']):
            print(f"Removing line {i+1}: {line.strip()}")
            continue
        
        # Skip lines that are part of CVV validation
        if 'cvv' in line.lower() and any(term in line.lower() for term in ['validate', 'digit', 'length']):
            print(f"Removing CVV validation line {i+1}: {line.strip()}")
            continue
            
        # Skip CVV-related method calls
        if 'test_cvv_retrieval' in line:
            print(f"Removing CVV test line {i+1}: {line.strip()}")
            continue
            
        filtered_lines.append(line)
    
    # Join lines back
    new_content = '\n'.join(filtered_lines)
    
    # Additional regex replacements for CVV parameters
    replacements = [
        # Remove CVV parameters from method calls
        (r', cvv="?"', ''),
        (r', cvv', ''),
        (r'cvv="?[^"]*"?,?\s*', ''),
        (r'self\.cvv\s*=\s*cvv\s*\n', ''),
        (r'self\.cvv\s*=\s*[^,\n]*[,\n]', ''),
        # Remove CVV from PreloadThread calls
        (r'PreloadThread\([^)]*,\s*cvv\)', lambda m: m.group(0).replace(', cvv', '')),
        # Remove CVV status messages
        (r'.*CVV.*provided.*\n', ''),
        (r'.*CVV.*not provided.*\n', ''),
        # Remove CVV automation messages
        (r'.*CVV automation removed.*\n', ''),
    ]
    
    for pattern, replacement in replacements:
        if callable(replacement):
            new_content = re.sub(pattern, replacement, new_content, flags=re.MULTILINE)
        else:
            new_content = re.sub(pattern, replacement, new_content, flags=re.MULTILINE)
    
    # Write back if changed
    if new_content != original_content:
        main_file.write_text(new_content, encoding='utf-8')
        print(f"✅ Removed CVV references from {main_file}")
        return True
    else:
        print("ℹ️ No CVV references found to remove")
        return True

if __name__ == "__main__":
    remove_cvv_references()
