#!/usr/bin/env python3
"""
MFFUHijack Console Scanner
Console-only version with Tesseract OCR exclusively
Clean menu-driven interface for livestream code detection
"""

import os
import sys
import time
import json
import threading
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import subprocess
import logging

# Suppress ALL browser-related logging - COMPLETE silence
logging.basicConfig(level=logging.CRITICAL)
import warnings
warnings.filterwarnings("ignore")

# Suppress all known noisy loggers
for logger_name in ['selenium', 'urllib3', 'requests', 'tensorflow', 'absl', 'chromium', 'chrome']:
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.CRITICAL)
    logger.disabled = True

# Add support folder to path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'MFFUHijack_Support'))

# Import console OCR module
try:
    from console_ocr import console_ocr, is_ocr_available
    import cv2
    import numpy as np
    OCR_AVAILABLE = True
except ImportError as e:
    print(f"Warning: OCR or video processing not available: {e}")
    OCR_AVAILABLE = False

class ConsoleInterface:
    """Clean console interface manager"""
    
    def __init__(self):
        self.banner = """
███╗   ███╗███████╗███████╗██╗   ██╗██╗  ██╗██╗     ██╗ █████╗  ██████╗██╗  ██╗
████╗ ████║██╔════╝██╔════╝██║   ██║██║  ██║██║     ██║██╔══██╗██╔════╝██║ ██╔╝
██╔████╔██║█████╗  █████╗  ██║   ██║███████║██║     ██║███████║██║     █████╔╝ 
██║╚██╔╝██║██╔══╝  ██╔══╝  ██║   ██║██╔══██║██║██   ██║██╔══██║██║     ██╔═██╗ 
██║ ╚═╝ ██║██║     ██║     ╚██████╔╝██║  ██║██║╚█████╔╝██║  ██║╚██████╗██║  ██╗
╚═╝     ╚═╝╚═╝     ╚═╝      ╚═════╝ ╚═╝  ╚═╝╚═╝ ╚════╝ ╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝
                                                                                
                    Console Scanner v1.0 - Tesseract OCR Engine
        """
    
    def clear_screen(self):
        """Clear console screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def show_banner(self):
        """Display MFFUHijack banner"""
        print(self.banner)
    
    def show_main_menu(self):
        """Display main menu"""
        self.clear_screen()
        self.show_banner()
        print("=" * 80)
        print("                              MAIN MENU")
        print("=" * 80)
        print()
        print("  [1] Real-Time Scanner     - Scan live YouTube streams")
        print("  [2] Test Scanner          - Test with downloaded streams")
        print("  [3] Exit                  - Close application")
        print()
        print("=" * 80)
    
    def get_menu_choice(self, max_choice: int) -> int:
        """Get valid menu choice from user"""
        while True:
            try:
                choice = input(f"\nSelect option (1-{max_choice}): ").strip()
                choice_num = int(choice)
                if 1 <= choice_num <= max_choice:
                    return choice_num
                else:
                    print(f"Please enter a number between 1 and {max_choice}")
            except ValueError:
                print("Please enter a valid number")
            except KeyboardInterrupt:
                print("\nExiting...")
                sys.exit(0)
    
    def get_input(self, prompt: str, required: bool = True) -> str:
        """Get input from user with validation"""
        while True:
            try:
                value = input(prompt).strip()
                if required and not value:
                    print("This field is required. Please enter a value.")
                    continue
                return value
            except KeyboardInterrupt:
                print("\nOperation cancelled")
                return ""
    
    def get_multiselect(self, prompt: str, options: List[str]) -> List[str]:
        """Get multiple selections from user"""
        print(f"\n{prompt}")
        print("Enter numbers separated by commas (e.g., 1,3):")
        
        for i, option in enumerate(options, 1):
            print(f"  [{i}] {option}")
        
        while True:
            try:
                selections = input("\nYour selection: ").strip()
                if not selections:
                    return []
                
                indices = [int(x.strip()) for x in selections.split(',')]
                selected = []
                
                for idx in indices:
                    if 1 <= idx <= len(options):
                        selected.append(options[idx - 1])
                    else:
                        print(f"Invalid selection: {idx}")
                        break
                else:
                    return selected
                    
            except ValueError:
                print("Please enter valid numbers separated by commas")
            except KeyboardInterrupt:
                print("\nSelection cancelled")
                return []

    def get_credentials(self, credentials_manager: 'CredentialsManager') -> Tuple[str, str]:
        """Get credentials with option to use saved ones"""
        if credentials_manager.has_credentials():
            username, password = credentials_manager.get_credentials()
            source = credentials_manager.get_credentials_source()
            print(f"\n✅ Found saved credentials for: {username} (from {source} version)")
            print("  [1] Use saved credentials")
            print("  [2] Enter new credentials")
            print("  [3] Clear saved credentials")

            choice = self.get_menu_choice(3)

            if choice == 1:
                return username, password
            elif choice == 2:
                return self._get_new_credentials(credentials_manager)
            elif choice == 3:
                credentials_manager.clear_credentials()
                print("Saved credentials cleared.")
                return self._get_new_credentials(credentials_manager)
        else:
            print("\n📝 No saved credentials found.")
            return self._get_new_credentials(credentials_manager)

    def _get_new_credentials(self, credentials_manager: 'CredentialsManager') -> Tuple[str, str]:
        """Get new credentials from user"""
        username = self.get_input("Username: ")
        password = self.get_input("Password: ", required=False)

        if username:
            save_choice = input("Save credentials for future use? (y/n): ").strip().lower()
            if save_choice in ['y', 'yes']:
                credentials_manager.set_credentials(username, password)
                print("✅ Credentials saved.")

        return username, password

class CredentialsManager:
    """Manage user credentials with persistent storage"""

    def __init__(self):
        self.credentials_file = "user_credentials.json"
        self.credentials = self.load_credentials()

    def load_credentials(self) -> Dict[str, str]:
        """Load saved credentials from console and GUI versions"""
        credentials = {}

        # First, try to load from GUI version (QSettings)
        gui_credentials = self.load_gui_credentials()
        if gui_credentials:
            credentials.update(gui_credentials)
            print(f"✅ Found GUI credentials for: {gui_credentials.get('username', 'unknown')}")

        # Then, try to load from console version (JSON file)
        console_credentials = self.load_console_credentials()
        if console_credentials:
            # Console credentials override GUI credentials if both exist
            credentials.update(console_credentials)
            if gui_credentials and console_credentials:
                print(f"✅ Using console credentials (overriding GUI): {console_credentials.get('username', 'unknown')}")

        return credentials

    def load_gui_credentials(self) -> Dict[str, str]:
        """Load credentials from GUI version (QSettings)"""
        try:
            # Try to import PyQt6 to access QSettings
            from PyQt6.QtCore import QSettings, QCoreApplication

            # Set application info for QSettings
            QCoreApplication.setApplicationName("MFFUHijack")
            QCoreApplication.setOrganizationName("jordan")

            settings = QSettings()
            username = settings.value("browser/username", "", type=str)
            password = settings.value("browser/password", "", type=str)

            if username and password:
                return {
                    'username': username,
                    'password': password,
                    'source': 'GUI'
                }
        except ImportError:
            # PyQt6 not available, skip GUI credentials
            pass
        except Exception as e:
            print(f"Warning: Could not load GUI credentials: {e}")

        return {}

    def load_console_credentials(self) -> Dict[str, str]:
        """Load saved credentials from console JSON file"""
        try:
            if os.path.exists(self.credentials_file):
                with open(self.credentials_file, 'r') as f:
                    data = json.load(f)
                    if data.get('username'):
                        data['source'] = 'Console'
                        return data
        except Exception as e:
            print(f"Warning: Could not load console credentials: {e}")
        return {}

    def save_credentials(self):
        """Save console credentials to JSON file (not GUI credentials)"""
        try:
            # Only save if credentials are from console or being updated
            if self.credentials.get('source') == 'Console':
                with open(self.credentials_file, 'w') as f:
                    json.dump(self.credentials, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not save credentials: {e}")

    def get_credentials(self) -> Tuple[str, str]:
        """Get stored username and password"""
        username = self.credentials.get('username', '')
        password = self.credentials.get('password', '')
        return username, password

    def get_credentials_source(self) -> str:
        """Get the source of current credentials"""
        return self.credentials.get('source', 'Unknown')

    def set_credentials(self, username: str, password: str):
        """Store username and password in console format"""
        self.credentials = {
            'username': username,
            'password': password,
            'source': 'Console'
        }
        self.save_credentials()

    def has_credentials(self) -> bool:
        """Check if credentials are stored"""
        username, password = self.get_credentials()
        return bool(username)

    def clear_credentials(self):
        """Clear stored console credentials (GUI credentials remain unchanged)"""
        # Only clear console credentials file, leave GUI credentials intact
        self.credentials = {}
        try:
            if os.path.exists(self.credentials_file):
                os.remove(self.credentials_file)
                print("Console credentials cleared.")
        except Exception as e:
            print(f"Warning: Could not clear credentials file: {e}")

        # Reload to check if GUI credentials are still available
        self.credentials = self.load_credentials()

class HistoryManager:
    """Manage scan history with persistent storage"""

    def __init__(self):
        self.history_file = "scan_history.json"
        self.history = self.load_history()

    def load_history(self) -> List[Dict]:
        """Load scan history from file"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Warning: Could not load history: {e}")
        return []

    def save_history(self):
        """Save scan history to file"""
        try:
            with open(self.history_file, 'w') as f:
                json.dump(self.history, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not save history: {e}")

    def add_detection(self, code: str, account_type: str, source: str):
        """Add new code detection to history"""
        detection = {
            'timestamp': datetime.now().isoformat(),
            'code': code,
            'account_type': account_type,
            'source': source,
            'validated': False
        }
        self.history.append(detection)
        self.save_history()

    def mark_validated(self, code: str):
        """Mark a code as validated"""
        for detection in reversed(self.history):
            if detection['code'] == code:
                detection['validated'] = True
                break
        self.save_history()

    def get_recent_history(self, limit: int = 20) -> List[Dict]:
        """Get recent detection history"""
        return self.history[-limit:] if self.history else []

    def show_history(self, console: ConsoleInterface):
        """Display scan history"""
        console.clear_screen()
        console.show_banner()
        print("=" * 80)
        print("                            SCAN HISTORY")
        print("=" * 80)

        recent = self.get_recent_history()
        if not recent:
            print("\nNo scan history available.")
        else:
            print(f"\nShowing last {len(recent)} detections:")
            print()
            for detection in reversed(recent):
                timestamp = datetime.fromisoformat(detection['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
                status = "✅ VALIDATED" if detection['validated'] else "⏳ PENDING"
                print(f"[{timestamp}] {detection['code']} - {detection['account_type']} - {status}")

        print("\n" + "=" * 80)
        input("Press Enter to continue...")

class TesseractScanner:
    """Tesseract OCR scanner for code detection"""
    
    def __init__(self):
        self.ocr_engine = console_ocr
        self.tesseract_available = is_ocr_available()
        
        if not self.tesseract_available:
            print("Error: Tesseract OCR not available!")
            print("Please install Tesseract OCR:")
            print("Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki")
            print("macOS: brew install tesseract")
            print("Linux: sudo apt install tesseract-ocr")
    
    def is_available(self) -> bool:
        """Check if scanner is available"""
        return self.tesseract_available
    
    def detect_codes_in_frame(self, frame, enabled_types: List[str]) -> List[Tuple[str, str]]:
        """Detect codes in video frame"""
        if not self.tesseract_available:
            return []
        
        return self.ocr_engine.detect_codes_in_frame(frame, enabled_types)

class ScannerStats:
    """Track scanning statistics"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """Reset all statistics"""
        self.start_time = time.time()
        self.frames_processed = 0
        self.codes_detected = 0
        self.codes_validated = 0
        self.code_types = {'Starter': 0, 'Starter Plus': 0, 'Expert': 0}
    
    def increment_frames(self):
        """Increment frames processed"""
        self.frames_processed += 1
    
    def add_detection(self, account_type: str):
        """Add code detection"""
        self.codes_detected += 1
        if account_type in self.code_types:
            self.code_types[account_type] += 1
    
    def add_validation(self):
        """Add code validation"""
        self.codes_validated += 1
    
    def get_runtime(self) -> str:
        """Get formatted runtime"""
        elapsed = int(time.time() - self.start_time)
        hours = elapsed // 3600
        minutes = (elapsed % 3600) // 60
        seconds = elapsed % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

def main():
    """Main application entry point"""
    console = ConsoleInterface()
    history = HistoryManager()
    credentials = CredentialsManager()
    scanner = TesseractScanner()

    if not OCR_AVAILABLE or not scanner.is_available():
        print("\nRequired dependencies not available:")
        print("- Tesseract OCR")
        print("- OpenCV (cv2)")
        print("- NumPy")
        print("\nPlease install missing dependencies and try again.")
        input("Press Enter to exit...")
        return

    while True:
        console.show_main_menu()
        choice = console.get_menu_choice(3)

        if choice == 1:
            # Real-Time Scanner
            run_realtime_scanner(console, history, credentials, scanner)
        elif choice == 2:
            # Test Scanner
            run_test_scanner(console, history, credentials, scanner)
        elif choice == 3:
            # Exit
            console.clear_screen()
            print("Thank you for using MFFUHijack Console Scanner!")
            break

def run_realtime_scanner(console: ConsoleInterface, history: HistoryManager, credentials: CredentialsManager, scanner: TesseractScanner):
    """Run real-time scanner configuration and execution"""
    console.clear_screen()
    console.show_banner()
    print("=" * 80)
    print("                         REAL-TIME SCANNER SETUP")
    print("=" * 80)

    # Get configuration
    print("\n1. YouTube Livestream Configuration:")
    url = console.get_input("Enter YouTube livestream URL: ")
    if not url:
        return

    print("\n2. Account Type Selection:")
    account_types = ['Starter', 'Starter Plus', 'Expert']
    selected_types = console.get_multiselect("Select account types to scan for:", account_types)
    if not selected_types:
        print("No account types selected. Returning to main menu.")
        input("Press Enter to continue...")
        return

    print("\n3. Account Credentials:")
    username, password = console.get_credentials(credentials)
    if not username:
        return

    print("\n4. Additional Options:")
    print("  [1] Start scanning now")
    print("  [2] View scan history")
    print("  [3] Return to main menu")

    option = console.get_menu_choice(3)

    if option == 1:
        start_realtime_scan(console, history, scanner, url, selected_types, username, password)
    elif option == 2:
        history.show_history(console)
        run_realtime_scanner(console, history, credentials, scanner)  # Return to config
    elif option == 3:
        return

def run_test_scanner(console: ConsoleInterface, history: HistoryManager, credentials: CredentialsManager, scanner: TesseractScanner):
    """Run test scanner configuration and execution"""
    console.clear_screen()
    console.show_banner()
    print("=" * 80)
    print("                           TEST SCANNER SETUP")
    print("=" * 80)

    # Create test_streams directory if it doesn't exist
    test_dir = "test_streams"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)

    print("\n1. Stream Selection:")
    print("  [1] Select existing stream file")
    print("  [2] Download new stream")
    print("  [3] Return to main menu")

    choice = console.get_menu_choice(3)

    if choice == 1:
        # Select existing file
        files = [f for f in os.listdir(test_dir) if f.endswith(('.mp4', '.mkv', '.avi', '.mov'))]
        if not files:
            print("No stream files found in test_streams directory.")
            input("Press Enter to continue...")
            return

        print("\nAvailable stream files:")
        for i, file in enumerate(files, 1):
            print(f"  [{i}] {file}")

        file_choice = console.get_menu_choice(len(files))
        selected_file = os.path.join(test_dir, files[file_choice - 1])

    elif choice == 2:
        # Download new stream
        url = console.get_input("Enter YouTube URL to download: ")
        if not url:
            return

        print("Downloading stream... (this may take a while)")
        # Note: Actual download implementation would use yt-dlp
        print("Download feature requires yt-dlp implementation")
        input("Press Enter to continue...")
        return

    elif choice == 3:
        return

    # Continue with test scanner configuration
    configure_test_scanner(console, history, credentials, scanner, selected_file)

def configure_test_scanner(console: ConsoleInterface, history: HistoryManager, credentials: CredentialsManager, scanner: TesseractScanner, video_file: str):
    """Configure test scanner parameters"""
    console.clear_screen()
    console.show_banner()
    print("=" * 80)
    print("                      TEST SCANNER CONFIGURATION")
    print("=" * 80)

    print(f"\nSelected file: {os.path.basename(video_file)}")

    # Get scan interval
    print("\n1. Scan Configuration:")
    while True:
        try:
            interval = float(console.get_input("Scan interval (seconds, default 5): ") or "5")
            if interval > 0:
                break
            print("Interval must be greater than 0")
        except ValueError:
            print("Please enter a valid number")

    # Get start time
    print("\n2. Start Time (HH:MM:SS format, default 00:00:00):")
    start_time = console.get_input("Start time: ") or "00:00:00"

    # Get account types
    print("\n3. Account Type Selection:")
    account_types = ['Starter', 'Starter Plus', 'Expert']
    selected_types = console.get_multiselect("Select account types to scan for:", account_types)
    if not selected_types:
        print("No account types selected. Returning to main menu.")
        input("Press Enter to continue...")
        return

    # Get credentials
    print("\n4. Account Credentials:")
    username, password = console.get_credentials(credentials)
    if not username:
        return

    # Start options
    print("\n5. Options:")
    print("  [1] Start test scan")
    print("  [2] View scan history")
    print("  [3] Return to main menu")

    option = console.get_menu_choice(3)

    if option == 1:
        start_test_scan(console, history, scanner, video_file, interval, start_time, selected_types, username, password)
    elif option == 2:
        history.show_history(console)
        configure_test_scanner(console, history, credentials, scanner, video_file)
    elif option == 3:
        return

def preload_browser_pages(console: ConsoleInterface, account_types: List[str], username: str, password: str) -> bool:
    """Preload browser pages for selected account types - EXACT same as GUI version with COMPLETE output suppression"""
    console.clear_screen()
    console.show_banner()
    print("=" * 80)
    print("                         BROWSER PRELOADING")
    print("=" * 80)

    print(f"\nAccount Types: {', '.join(account_types)}")
    print(f"Username: {username}")
    print("\n🌐 Starting browser preloading process...")

    try:
        # COMPLETE output suppression - redirect ALL output to null
        import sys
        import os
        import logging
        from contextlib import redirect_stdout, redirect_stderr

        # Suppress ALL logging
        logging.basicConfig(level=logging.CRITICAL)
        for logger_name in ['selenium', 'urllib3', 'requests', 'tensorflow', 'absl']:
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.CRITICAL)
            logger.disabled = True

        # Disable all warnings
        import warnings
        warnings.filterwarnings("ignore")

        # Import browser automation - EXACT same as GUI version
        sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'MFFUHijack_Support'))

        from browser_automation import browser_automation  # Use global instance like GUI

        print("🌐 Initializing browser automation...")

        # Redirect ALL output to null during browser operations
        with open(os.devnull, 'w') as devnull:
            with redirect_stdout(devnull), redirect_stderr(devnull):
                # Use EXACT same workflow as GUI version
                success = browser_automation.login_to_mff(username, password)

        if success:
            print("✅ Login initiated - complete CAPTCHA if present")

            # Wait for login completion like GUI version (with output suppression)
            with open(os.devnull, 'w') as devnull:
                with redirect_stdout(devnull), redirect_stderr(devnull):
                    login_completed = browser_automation.wait_for_login_completion(timeout=60)

            if login_completed:
                print("✅ Login completed successfully")

                print(f"🚀 Preloading pages for selected types: {', '.join(account_types)}")

                # Use open_manual_tabs method - EXACT same as GUI version (with output suppression)
                with open(os.devnull, 'w') as devnull:
                    with redirect_stdout(devnull), redirect_stderr(devnull):
                        success = browser_automation.open_manual_tabs(account_types)

                if success:
                    print("✅ Selected account pages preloaded to checkout stage")
                    print("💳 Coupon textboxes focused and ready for instant code entry")
                    print("\n🎯 Browser is ready for instant code submission")
                    print("💡 Keep browser window open during scanning")

                    input("Press Enter to continue to scanning...")
                    return True
                else:
                    print("❌ Failed to preload pages")
            else:
                print("⚠️ Login timeout - please complete login manually")
        else:
            print("❌ Login failed")

        input("Press Enter to continue without browser preloading...")
        return False

    except ImportError:
        print("❌ Browser automation not available")
        print("💡 Install selenium for browser functionality: pip install selenium")
        input("Press Enter to continue without browser preloading...")
        return False
    except Exception as e:
        print(f"❌ Browser preloading error: {e}")
        input("Press Enter to continue without browser preloading...")
        return False

def submit_code_to_browser(code: str, account_type: str):
    """Submit detected code to browser - EXACT same as GUI version with COMPLETE output suppression"""
    try:
        # COMPLETE output suppression
        import sys
        import os
        import logging
        import warnings
        from contextlib import redirect_stdout, redirect_stderr

        # Suppress ALL logging and warnings
        logging.basicConfig(level=logging.CRITICAL)
        for logger_name in ['selenium', 'urllib3', 'requests', 'tensorflow', 'absl']:
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.CRITICAL)
            logger.disabled = True
        warnings.filterwarnings("ignore")

        # Import browser automation - EXACT same as GUI version
        sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'MFFUHijack_Support'))

        from browser_automation import browser_automation  # Use global instance like GUI

        print(f"🚀 Submitting code {code} for {account_type}...")

        # Use EXACT same method as GUI version with complete output suppression
        reset_account = "" if account_type.lower() != "reset" else ""  # Empty for console version

        with open(os.devnull, 'w') as devnull:
            with redirect_stdout(devnull), redirect_stderr(devnull):
                success = browser_automation.submit_code(code, account_type, reset_account)

        if success:
            print(f"✅ Code {code} entered successfully - manual CVV entry required!")
        else:
            print(f"❌ Failed to enter code {code}")

    except Exception as e:
        print(f"❌ Code submission error: {e}")

def validate_code_in_browser(code: str, account_type: str):
    """Validate detected code in browser (test mode - no checkout) - EXACT same as GUI version with COMPLETE output suppression"""
    try:
        # COMPLETE output suppression
        import sys
        import os
        import logging
        import warnings
        from contextlib import redirect_stdout, redirect_stderr

        # Suppress ALL logging and warnings
        logging.basicConfig(level=logging.CRITICAL)
        for logger_name in ['selenium', 'urllib3', 'requests', 'tensorflow', 'absl']:
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.CRITICAL)
            logger.disabled = True
        warnings.filterwarnings("ignore")

        # Import browser automation - EXACT same as GUI version
        sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'MFFUHijack_Support'))

        from browser_automation import browser_automation  # Use global instance like GUI

        print(f"🔍 Validating code {code} for {account_type}...")

        # For test mode, use submit_code with complete output suppression
        reset_account = "" if account_type.lower() != "reset" else ""  # Empty for console version

        with open(os.devnull, 'w') as devnull:
            with redirect_stdout(devnull), redirect_stderr(devnull):
                success = browser_automation.submit_code(code, account_type, reset_account)

        if success:
            print(f"✅ Code {code} validated successfully!")
        else:
            print(f"❌ Code {code} validation failed")

    except Exception as e:
        print(f"❌ Code validation error: {e}")

def start_realtime_scan(console: ConsoleInterface, history: HistoryManager, scanner: TesseractScanner,
                       url: str, account_types: List[str], username: str, password: str):
    """Start real-time scanning with browser preloading"""
    # Step 1: Browser Preloading
    console.clear_screen()
    console.show_banner()
    print("=" * 80)
    print("                         REAL-TIME SCANNER SETUP")
    print("=" * 80)
    print(f"\nStream: {url}")
    print(f"Account Types: {', '.join(account_types)}")
    print(f"Username: {username}")

    print("\n🌐 BROWSER PRELOADING")
    print("=" * 40)
    print("Before starting the scan, we'll preload browser pages for instant code submission.")

    while True:
        choice = input("\nPreload browser pages? (Y/N): ").strip().upper()
        if choice in ['Y', 'YES']:
            browser_ready = preload_browser_pages(console, account_types, username, password)
            break
        elif choice in ['N', 'NO']:
            print("⚠️ Skipping browser preloading - codes will be detected but not submitted")
            browser_ready = False
            break
        else:
            print("Please enter Y or N")

    # Step 2: Final confirmation before scanning
    console.clear_screen()
    console.show_banner()
    print("=" * 80)
    print("                         READY TO START SCANNING")
    print("=" * 80)
    print(f"\nStream: {url}")
    print(f"Account Types: {', '.join(account_types)}")
    print(f"Browser Ready: {'✅ Yes' if browser_ready else '❌ No'}")

    while True:
        choice = input("\nStart scanning now? (Y/N): ").strip().upper()
        if choice in ['Y', 'YES']:
            break
        elif choice in ['N', 'NO']:
            print("Returning to main menu...")
            input("Press Enter to continue...")
            return
        else:
            print("Please enter Y or N")

    # Step 3: Start actual scanning
    console.clear_screen()
    console.show_banner()

    stats = ScannerStats()
    print("=" * 80)
    print("                         REAL-TIME SCANNER ACTIVE")
    print("=" * 80)
    print(f"\nStream: {url}")
    print(f"Account Types: {', '.join(account_types)}")
    print(f"Username: {username}")
    print(f"Browser Ready: {'✅ Yes' if browser_ready else '❌ No'}")
    print("\nPress Ctrl+C to stop scanning")
    print("=" * 80)

    # Note: Actual implementation would integrate with livestream capture
    print("\nReal-time scanning requires livestream integration (yt-dlp + cv2)")
    print("This is a placeholder for the actual scanning loop")

    try:
        while True:
            # Simulate scanning
            time.sleep(1)
            stats.increment_frames()

            # Update display
            console.clear_screen()
            console.show_banner()
            show_scan_dashboard(stats, account_types)

            # Simulate occasional detection
            if stats.frames_processed % 30 == 0 and stats.frames_processed > 0:
                code = f"TEST{stats.frames_processed}"
                account_type = account_types[0]
                stats.add_detection(account_type)
                history.add_detection(code, account_type, url)
                print(f"\n[{datetime.now().strftime('%H:%M:%S')}] CODE DETECTED: {code} - Account Type: {account_type}")

                # Submit code if browser is ready
                if browser_ready:
                    submit_code_to_browser(code, account_type)

    except KeyboardInterrupt:
        print("\n\nScanning stopped by user.")
        input("Press Enter to continue...")

def start_test_scan(console: ConsoleInterface, history: HistoryManager, scanner: TesseractScanner,
                   video_file: str, interval: float, start_time: str, account_types: List[str],
                   username: str, password: str):
    """Start test scanning with browser preloading"""
    # Step 1: Browser Preloading
    console.clear_screen()
    console.show_banner()
    print("=" * 80)
    print("                           TEST SCANNER SETUP")
    print("=" * 80)
    print(f"\nFile: {os.path.basename(video_file)}")
    print(f"Interval: {interval}s")
    print(f"Start Time: {start_time}")
    print(f"Account Types: {', '.join(account_types)}")
    print(f"Username: {username}")

    print("\n🌐 BROWSER PRELOADING")
    print("=" * 40)
    print("Before starting the test scan, we'll preload browser pages for code validation.")

    while True:
        choice = input("\nPreload browser pages? (Y/N): ").strip().upper()
        if choice in ['Y', 'YES']:
            browser_ready = preload_browser_pages(console, account_types, username, password)
            break
        elif choice in ['N', 'NO']:
            print("⚠️ Skipping browser preloading - codes will be detected but not validated")
            browser_ready = False
            break
        else:
            print("Please enter Y or N")

    # Step 2: Final confirmation before scanning
    console.clear_screen()
    console.show_banner()
    print("=" * 80)
    print("                         READY TO START TEST SCAN")
    print("=" * 80)
    print(f"\nFile: {os.path.basename(video_file)}")
    print(f"Interval: {interval}s")
    print(f"Start Time: {start_time}")
    print(f"Account Types: {', '.join(account_types)}")
    print(f"Browser Ready: {'✅ Yes' if browser_ready else '❌ No'}")

    while True:
        choice = input("\nStart test scanning now? (Y/N): ").strip().upper()
        if choice in ['Y', 'YES']:
            break
        elif choice in ['N', 'NO']:
            print("Returning to main menu...")
            input("Press Enter to continue...")
            return
        else:
            print("Please enter Y or N")

    # Step 3: Start actual scanning
    console.clear_screen()
    console.show_banner()

    stats = ScannerStats()
    print("=" * 80)
    print("                           TEST SCANNER ACTIVE")
    print("=" * 80)
    print(f"\nFile: {os.path.basename(video_file)}")
    print(f"Interval: {interval}s")
    print(f"Start Time: {start_time}")
    print(f"Account Types: {', '.join(account_types)}")
    print(f"Username: {username}")
    print(f"Browser Ready: {'✅ Yes' if browser_ready else '❌ No'}")
    print("\nPress Ctrl+C to stop scanning")
    print("=" * 80)

    if not OCR_AVAILABLE:
        print("\nVideo processing not available. Install opencv-python (cv2).")
        input("Press Enter to continue...")
        return

    try:
        # Open video file
        cap = cv2.VideoCapture(video_file)
        if not cap.isOpened():
            print(f"\nError: Could not open video file: {video_file}")
            input("Press Enter to continue...")
            return

        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        # Parse start time and calculate start frame
        start_frame = parse_time_to_frame(start_time, fps)
        if start_frame > 0:
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)

        # Calculate frame interval
        frame_interval = int(fps * interval)

        print(f"\nVideo Info: {total_frames} frames, {fps:.2f} FPS")
        print(f"Starting at frame {start_frame}, scanning every {frame_interval} frames")

        last_scan_time = time.time()

        while True:
            ret, frame = cap.read()
            if not ret:
                print("\nEnd of video reached.")
                break

            stats.increment_frames()

            # Check if it's time to scan
            current_time = time.time()
            if current_time - last_scan_time >= interval:
                # Detect codes in current frame
                detected_codes = scanner.detect_codes_in_frame(frame, account_types)

                for code, account_type in detected_codes:
                    stats.add_detection(account_type)
                    history.add_detection(code, account_type, os.path.basename(video_file))

                    # Update display with detection
                    console.clear_screen()
                    console.show_banner()
                    show_scan_dashboard(stats, account_types)
                    print(f"\n[{datetime.now().strftime('%H:%M:%S')}] CODE DETECTED: {code} - Account Type: {account_type}")

                    # Validate code if browser is ready (test mode - no checkout button press)
                    if browser_ready:
                        validate_code_in_browser(code, account_type)

                last_scan_time = current_time

            # Update display periodically
            if stats.frames_processed % (frame_interval * 5) == 0:
                console.clear_screen()
                console.show_banner()
                show_scan_dashboard(stats, account_types)

            # Skip frames to maintain interval
            for _ in range(frame_interval - 1):
                cap.read()
                stats.increment_frames()

        cap.release()

    except KeyboardInterrupt:
        print("\n\nScanning stopped by user.")
        if 'cap' in locals():
            cap.release()
        input("Press Enter to continue...")
    except Exception as e:
        print(f"\nError during scanning: {e}")
        if 'cap' in locals():
            cap.release()
        input("Press Enter to continue...")

def parse_time_to_frame(time_str: str, fps: float) -> int:
    """Parse HH:MM:SS time string to frame number"""
    try:
        parts = time_str.split(':')
        if len(parts) != 3:
            return 0

        hours = int(parts[0])
        minutes = int(parts[1])
        seconds = int(parts[2])

        total_seconds = hours * 3600 + minutes * 60 + seconds
        return int(total_seconds * fps)

    except (ValueError, IndexError):
        return 0

def show_scan_dashboard(stats: ScannerStats, account_types: List[str]):
    """Display real-time scanning dashboard"""
    print("=" * 80)
    print("                           SCANNING DASHBOARD")
    print("=" * 80)

    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    runtime = stats.get_runtime()

    print(f"\nCurrent Time:      {current_time}")
    print(f"Runtime:           {runtime}")
    print(f"Frames Processed:  {stats.frames_processed}")
    print(f"Codes Detected:    {stats.codes_detected}")
    print(f"Codes Validated:   {stats.codes_validated}")

    print(f"\nCode Types Found:")
    for account_type in account_types:
        count = stats.code_types.get(account_type, 0)
        print(f"  {account_type}: {count}")

    print("\n" + "=" * 80)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nApplication terminated by user.")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        input("Press Enter to exit...")
