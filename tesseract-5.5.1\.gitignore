*~
# Windows
*.user.*
*.idea*
*.log
*.tlog
*.cache
*.obj
*.sdf
*.opensdf
*.lastbuildstate
*.unsuccessfulbuild
*.suo
*.res
*.ipch
*.manifest

# Linux
# ignore local configuration
config.*
config/*
Makefile
Makefile.in
*.m4

# ignore help scripts/files
configure
libtool
stamp-h1
tesseract.pc
config_auto.h
/doc/html/*
/doc/*.1
/doc/*.5
/doc/*.html
/doc/*.xml

# generated version file
/include/tesseract/version.h

# executables
/tesseract
/src/training/ambiguous_words
/src/training/classifier_tester
/src/training/cntraining
/src/training/combine_tessdata
/src/training/dawg2wordlist
/src/training/merge_unicharsets
/src/training/mftraining
/src/training/set_unicharset_properties
/src/training/shapeclustering
/src/training/text2image
/src/training/unicharset_extractor
/src/training/wordlist2dawg

*.patch

# files generated by libtool
/src/training/combine_lang_model
/src/training/lstmeval
/src/training/lstmtraining

# ignore compilation files
build/*
/bin
/cmake-*
.deps
.dirstamp
/.libs
*/.libs/*
*/*/.deps/*
*/*/.libs/*
*.lo
*.la
*.o
*.Plo
*.a
*.class
*.jar
__pycache__

# tessdata
*.traineddata
tessdata_*

# build dirs
/build*
/*.dll
/*.lib
/*.exe
/*.lnk
/win*
.vs*
.s*

# files generated by "make check"
/tests/.dirstamp
/unittest/*.trs
/unittest/tmp/*

# test programs
/unittest/*_test
/unittest/primesbitvector
/unittest/primesmap

# generated files from unlvtests
times.txt
/unlvtests/results*

# snap packaging specific rules
/parts/
/stage/
/prime/
/snap/.snapcraft/

/*.snap
/*_source.tar.bz2
