@echo off
title MFFUHijack Console Scanner
color 0A

echo.
echo ===============================================================================
echo                         MFFUHIJACK CONSOLE SCANNER
echo ===============================================================================
echo.
echo Starting MFFUHijack Console Scanner...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    echo.
    pause
    exit /b 1
)

REM Check if we're in the correct directory
if not exist "mffuhijack_console.py" (
    echo ERROR: mffuhijack_console.py not found
    echo Please run this batch file from the console_version directory
    echo.
    pause
    exit /b 1
)

REM Run the console application
python mffuhijack_console.py

REM Keep window open if there was an error
if errorlevel 1 (
    echo.
    echo ===============================================================================
    echo Application exited with an error. Check the output above for details.
    echo ===============================================================================
    echo.
    pause
)

echo.
echo Thank you for using MFFUHijack Console Scanner!
pause
