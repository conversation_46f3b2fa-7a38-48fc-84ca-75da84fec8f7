#!/usr/bin/env python3
"""
Debug script for Tesseract OCR text detection issues
"""

import sys
import os
import numpy as np
import cv2

# Add support folder to path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'MFFUHijack_Support'))

def test_basic_tesseract():
    """Test basic Tesseract functionality"""
    print("🔍 Testing basic Tesseract functionality...")
    
    try:
        import pytesseract
        from PIL import Image
        
        # Set Tesseract path for Windows
        import platform
        if platform.system() == "Windows":
            tesseract_path = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
            if os.path.exists(tesseract_path):
                pytesseract.pytesseract.tesseract_cmd = tesseract_path
                print(f"✅ Set Tesseract path: {tesseract_path}")
        
        # Create a simple test image with clear text
        img = np.ones((100, 400, 3), dtype=np.uint8) * 255  # White background
        cv2.putText(img, "HELLO WORLD", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # Convert to PIL Image
        pil_img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        
        # Test basic OCR
        text = pytesseract.image_to_string(pil_img).strip()
        print(f"📝 Basic OCR result: '{text}'")
        
        if text:
            print("✅ Basic Tesseract is working!")
            return True
        else:
            print("❌ Basic Tesseract returned no text")
            return False
            
    except Exception as e:
        print(f"❌ Basic Tesseract test failed: {e}")
        return False

def test_tesseract_with_data():
    """Test Tesseract with detailed data output"""
    print("\n🔍 Testing Tesseract with detailed data...")
    
    try:
        import pytesseract
        from PIL import Image
        
        # Set Tesseract path
        import platform
        if platform.system() == "Windows":
            tesseract_path = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
            if os.path.exists(tesseract_path):
                pytesseract.pytesseract.tesseract_cmd = tesseract_path
        
        # Create test image with code-like text
        img = np.ones((100, 600, 3), dtype=np.uint8) * 255
        cv2.putText(img, "USE CODE: ABC123", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # Convert to PIL Image
        pil_img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        
        # Test with image_to_data
        data = pytesseract.image_to_data(pil_img, output_type=pytesseract.Output.DICT)
        
        print(f"📊 Data keys: {list(data.keys())}")
        print(f"📊 Number of boxes: {len(data['level'])}")
        
        # Process results
        results = []
        n_boxes = len(data['level'])
        for i in range(n_boxes):
            confidence = float(data['conf'][i])
            text = data['text'][i].strip()
            
            if confidence > 0 and text:
                results.append({
                    'text': text,
                    'confidence': confidence,
                    'level': data['level'][i]
                })
                print(f"   📝 Text: '{text}' (conf: {confidence:.1f}, level: {data['level'][i]})")
        
        if results:
            print(f"✅ Detailed Tesseract found {len(results)} text elements")
            return True
        else:
            print("❌ Detailed Tesseract found no text")
            return False
            
    except Exception as e:
        print(f"❌ Detailed Tesseract test failed: {e}")
        return False

def test_tesseract_engine_class():
    """Test our TesseractOCREngine class"""
    print("\n🔍 Testing TesseractOCREngine class...")
    
    try:
        from ocr_utils import TesseractOCREngine
        
        # Create engine instance
        engine = TesseractOCREngine()
        print(f"📊 Engine name: {engine.name}")
        print(f"📊 Engine available: {engine.is_available()}")
        
        if not engine.is_available():
            print("❌ TesseractOCREngine reports not available")
            return False
        
        # Create test image
        img = np.ones((100, 600, 3), dtype=np.uint8) * 255
        cv2.putText(img, "USE CODE: XYZ789", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # Test with raw image (no preprocessing)
        print("🎨 Testing with raw image...")
        results_raw = engine.extract_text(img, use_raw_image=True)
        print(f"📊 Raw results: {len(results_raw)}")
        for result in results_raw:
            print(f"   📝 '{result.get('text', '')}' (conf: {result.get('confidence', 0):.3f})")
        
        # Test with preprocessing
        print("🔧 Testing with preprocessing...")
        results_processed = engine.extract_text(img, use_raw_image=False)
        print(f"📊 Processed results: {len(results_processed)}")
        for result in results_processed:
            print(f"   📝 '{result.get('text', '')}' (conf: {result.get('confidence', 0):.3f})")
        
        if results_raw or results_processed:
            print("✅ TesseractOCREngine is working!")
            return True
        else:
            print("❌ TesseractOCREngine returned no results")
            return False
            
    except Exception as e:
        print(f"❌ TesseractOCREngine test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_configs():
    """Test different Tesseract configurations"""
    print("\n🔍 Testing different Tesseract configurations...")
    
    try:
        import pytesseract
        from PIL import Image
        
        # Set Tesseract path
        import platform
        if platform.system() == "Windows":
            tesseract_path = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
            if os.path.exists(tesseract_path):
                pytesseract.pytesseract.tesseract_cmd = tesseract_path
        
        # Create test image
        img = np.ones((100, 600, 3), dtype=np.uint8) * 255
        cv2.putText(img, "CODE: TEST123", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        pil_img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        
        # Test different PSM modes
        configs = [
            ('PSM 6 (Uniform block)', '--psm 6'),
            ('PSM 7 (Single text line)', '--psm 7'),
            ('PSM 8 (Single word)', '--psm 8'),
            ('PSM 13 (Raw line)', '--psm 13'),
            ('Default', ''),
        ]
        
        best_result = None
        best_config = None
        
        for config_name, config_str in configs:
            try:
                text = pytesseract.image_to_string(pil_img, config=config_str).strip()
                print(f"   {config_name}: '{text}'")
                
                if text and (not best_result or len(text) > len(best_result)):
                    best_result = text
                    best_config = config_name
                    
            except Exception as e:
                print(f"   {config_name}: Error - {e}")
        
        if best_result:
            print(f"✅ Best config: {best_config} -> '{best_result}'")
            return True
        else:
            print("❌ No configuration worked")
            return False
            
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def main():
    """Main debug function"""
    print("=" * 60)
    print("🐛 TESSERACT OCR DEBUG")
    print("=" * 60)
    
    tests = [
        ("Basic Tesseract", test_basic_tesseract),
        ("Detailed Data", test_tesseract_with_data),
        ("Engine Class", test_tesseract_engine_class),
        ("Different Configs", test_different_configs),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
    
    print(f"\n{'='*60}")
    print(f"🏁 DEBUG RESULTS: {passed}/{len(tests)} tests passed")
    
    if passed == 0:
        print("❌ Tesseract is completely broken - check installation")
    elif passed < len(tests):
        print("⚠️ Some Tesseract functionality working, some issues remain")
    else:
        print("✅ Tesseract appears to be working correctly")
    
    print("="*60)
    
    return passed > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
