name: unittest
# autotools build on ubuntu. unittests with address sanitizers. with openmp.
# ubuntu-20.04-gcc-unittest - CI runs out of diskspace.
on:
  #push:
  pull_request:
    paths:
      - '**.cpp'
      - '**.h'
      - '**Makefile.am'
      - '/configure.ac'
      - 'unittest/**.c'
      - 'unittest/**.cc'
  schedule:
    - cron: 0 0 * * *
  workflow_dispatch:

jobs:
  sanitizers:
    name: ${{ matrix.config.name }}
    runs-on: ${{ matrix.config.os }}
    strategy:
      fail-fast: false
      matrix:
        config:
          - { name: ubuntu-24.04-gcc-unittest, os: ubuntu-24.04, cxx: g++, cxxflags: '-g -O2 -fsanitize=address,undefined' }
          - { name: ubuntu-22.04-clang-unittest, os: ubuntu-22.04, cxx: clang++, cxxflags: '-g -O2 -fsanitize=address,undefined -stdlib=libc++' }
    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Remove Homebrew, Android and .NET to provide more disk space
      run: |
           # https://github.com/actions/virtual-environments/issues/2606#issuecomment-772683150
           sudo rm -rf /home/<USER>
           sudo rm -rf /usr/local/lib/android # will release about 10 GB if you don't need Android
           sudo rm -rf /usr/share/dotnet # will release about 20GB if you don't need .NET

    - name: Install dependencies (Linux)
      run: |
           sudo apt-get update
           sudo apt-get install autoconf libleptonica-dev libpango1.0-dev -y
           sudo apt-get install cabextract -y

    - name: Setup
      run: |
           ./autogen.sh

    - name: Configure (Linux)
      run: |
           ./configure '--disable-shared' 'CXX=${{ matrix.config.cxx }}' \
               'CXXFLAGS=${{ matrix.config.cxxflags }}'

    - name: Make and Install Tesseract
      run: |
           ${{ matrix.config.cxx }} --version
           make
           sudo make install

    - name: Make and Install Training Tools
      run: |
           make training
           sudo make training-install

    - name: Display Tesseract and Training Tools Version
      run: |
           tesseract -v
           lstmtraining -v
           text2image -v
      if: success() || failure()

    - name: Download fonts, tessdata and langdata required for tests
      run: |
           git clone https://github.com/egorpugin/tessdata tessdata_unittest
           cp tessdata_unittest/fonts/* test/testing/
           mv tessdata_unittest/* ../

    - name: Run Tesseract on phototest.tif and devatest.png
      run: |
           tesseract test/testing/phototest.tif -  --tessdata-dir ../tessdata
           tesseract test/testing/devatest.png - -l hin+eng  --tessdata-dir ../tessdata

    - name: Make and run Unit Tests
      run: |
           make check

    - name: Display Unit Tests Report and Compiler Version
      run: |
           cat test-suite.log
           ${{ matrix.config.cxx }} --version
           git log -3 --pretty=format:'%h %ad %s | %an'
      if: always()
