# For most projects, this workflow file will not need changing; you simply need
# to commit it to your repository.
#
# You may wish to alter this file to override the set of languages analyzed,
# or to provide custom queries or build logic.
#
# ******** NOTE ********
# We have attempted to detect the languages in your repository. Please check
# the `language` matrix defined below to confirm you have the correct set of
# supported CodeQL languages.
#
name: "CodeQL"

on:
  push:
    branches: [ main ]
    paths:
      - '**.cpp'
      - '**.h'
      - '**/codeql-analysis.yml'
      - 'm4/*.m4'
      - 'Makefile.am'
      - 'autogen.sh'
      - 'configure.ac'
  pull_request:
    # The branches below must be a subset of the branches above
    branches: [ main ]
    paths:
      - '**.cpp'
      - '**.h'
      - '**/codeql-analysis.yml'
      - 'm4/*.m4'
      - 'Makefile.am'
      - 'autogen.sh'
      - 'configure.ac'
  schedule:
    - cron: '34 23 * * 2'

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language: [ 'cpp' ]
        # CodeQL supports [ 'cpp', 'csharp', 'go', 'java', 'javascript', 'python' ]
        # Learn more:
        # https://docs.github.com/en/free-pro-team@latest/github/finding-security-vulnerabilities-and-errors-in-your-code/configuring-code-scanning#changing-the-languages-that-are-analyzed

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Install dependencies
      run: |
           sudo apt-get update
           sudo apt-get install autoconf libleptonica-dev -y
           sudo apt-get install libpango1.0-dev -y
           sudo apt-get install cabextract libarchive-dev -y
           sudo apt-get install libcurl4-openssl-dev libcurl4 curl -y

    # Initializes the CodeQL tools for scanning.
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: ${{ matrix.language }}
        # If you wish to specify custom queries, you can do so here or in a config file.
        # By default, queries listed here will override any specified in a config file.
        # Prefix the list here with "+" to use these queries and those in the config file.
        # queries: ./path/to/local/query, your-org/your-repo/queries@main

    - name: Build
      run: |
       ./autogen.sh
       ./configure
       make all training

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2
