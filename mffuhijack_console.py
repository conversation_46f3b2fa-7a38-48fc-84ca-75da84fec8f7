#!/usr/bin/env python3
"""
MFFUHijack Console Scanner
Console-only version with Tesseract OCR exclusively
Clean menu-driven interface for livestream code detection
"""

import os
import sys
import time
import json
import random
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# Add support folder to path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'MFFUHijack_Support'))

# Import console OCR module
try:
    from console_ocr import console_ocr, is_ocr_available
    import cv2
    import numpy as np
    OCR_AVAILABLE = True
except ImportError as e:
    print(f"Warning: OCR or video processing not available: {e}")
    OCR_AVAILABLE = False

# Import yt-dlp for livestream capture
try:
    import yt_dlp
    YTDLP_AVAILABLE = True
except ImportError:
    print("Warning: yt-dlp not available - livestream capture disabled")
    YTDLP_AVAILABLE = False

# Try to import colorama for cross-platform color support
try:
    from colorama import init, Fore, Back, Style
    init(autoreset=True)  # Automatically reset colors after each print
    COLORAMA_AVAILABLE = True
except ImportError:
    COLORAMA_AVAILABLE = False

# Enable ANSI colors on Windows if colorama not available
def enable_ansi_colors():
    """Enable ANSI color support on Windows"""
    if os.name == 'nt' and not COLORAMA_AVAILABLE:  # Windows without colorama
        try:
            import ctypes
            kernel32 = ctypes.windll.kernel32
            # Enable ANSI escape sequence processing
            kernel32.SetConsoleMode(kernel32.GetStdHandle(-11), 7)
        except:
            pass

# Enable colors at import
enable_ansi_colors()

# Color codes - use colorama if available, otherwise ANSI
class Colors:
    """Cross-platform color codes for console output"""
    if COLORAMA_AVAILABLE:
        # Use colorama colors (more reliable on Windows)
        LIGHT_GRAY = Fore.LIGHTWHITE_EX    # Light gray for main text
        SKY_BLUE = Fore.LIGHTCYAN_EX       # Sky blue for accent/highlights
        WHITE = Fore.WHITE                 # White for important text
        GREEN = Fore.LIGHTGREEN_EX         # Success messages
        RED = Fore.LIGHTRED_EX             # Error messages
        YELLOW = Fore.LIGHTYELLOW_EX       # Warning messages
        BOLD = Style.BRIGHT                # Bold text
        RESET = Style.RESET_ALL            # Reset to default
    else:
        # Fallback to ANSI codes
        LIGHT_GRAY = '\033[37m'      # Light gray for main text
        SKY_BLUE = '\033[94m'        # Sky blue for accent/highlights
        WHITE = '\033[97m'           # White for important text
        GREEN = '\033[92m'           # Success messages
        RED = '\033[91m'             # Error messages
        YELLOW = '\033[93m'          # Warning messages
        BOLD = '\033[1m'             # Bold text
        RESET = '\033[0m'            # Reset to default

    @staticmethod
    def colorize(text: str, color: str) -> str:
        """Apply color to text"""
        if COLORAMA_AVAILABLE:
            return f"{color}{text}{Style.RESET_ALL}"
        else:
            return f"{color}{text}{Colors.RESET}"

class ConsoleInterface:
    """Clean console interface manager with color support"""

    def __init__(self):
        self.banner = f"""
{Colors.SKY_BLUE}███╗   ███╗███████╗███████╗██╗   ██╗██╗  ██╗██╗     ██╗ █████╗  ██████╗██╗  ██╗
████╗ ████║██╔════╝██╔════╝██║   ██║██║  ██║██║     ██║██╔══██╗██╔════╝██║ ██╔╝
██╔████╔██║█████╗  █████╗  ██║   ██║███████║██║     ██║███████║██║     █████╔╝
██║╚██╔╝██║██╔══╝  ██╔══╝  ██║   ██║██╔══██║██║██   ██║██╔══██║██║     ██╔═██╗
██║ ╚═╝ ██║██║     ██║     ╚██████╔╝██║  ██║██║╚█████╔╝██║  ██║╚██████╗██║  ██╗
╚═╝     ╚═╝╚═╝     ╚═╝      ╚═════╝ ╚═╝  ╚═╝╚═╝ ╚════╝ ╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝{Colors.RESET}

{Colors.LIGHT_GRAY}                    Console Scanner v1.0 - Tesseract OCR Engine{Colors.RESET}
        """

    def clear_screen(self):
        """Clear console screen"""
        os.system('cls' if os.name == 'nt' else 'clear')

    def show_banner(self):
        """Display MFFUHijack banner"""
        print(self.banner)
    
    def show_main_menu(self):
        """Display main menu"""
        self.clear_screen()
        self.show_banner()
        print(Colors.colorize("=" * 80, Colors.SKY_BLUE))
        print(Colors.colorize("                              MAIN MENU", Colors.WHITE))
        print(Colors.colorize("=" * 80, Colors.SKY_BLUE))
        print()
        print(f"{Colors.LIGHT_GRAY}  {Colors.colorize('[1]', Colors.SKY_BLUE)} Real-Time Scanner     - Scan live YouTube streams")
        print(f"  {Colors.colorize('[2]', Colors.SKY_BLUE)} Test Scanner          - Test with downloaded streams")
        print(f"  {Colors.colorize('[3]', Colors.SKY_BLUE)} Exit                  - Close application{Colors.RESET}")
        print()
        print(Colors.colorize("=" * 80, Colors.SKY_BLUE))
    
    def get_menu_choice(self, max_choice: int) -> int:
        """Get valid menu choice from user"""
        while True:
            try:
                choice = input(f"\n{Colors.colorize('Select option', Colors.SKY_BLUE)} ({Colors.colorize(f'1-{max_choice}', Colors.WHITE)}): ").strip()
                choice_num = int(choice)
                if 1 <= choice_num <= max_choice:
                    return choice_num
                else:
                    print(Colors.colorize(f"Please enter a number between 1 and {max_choice}", Colors.YELLOW))
            except ValueError:
                print(Colors.colorize("Please enter a valid number", Colors.YELLOW))
            except KeyboardInterrupt:
                print(Colors.colorize("\nExiting...", Colors.RED))
                sys.exit(0)
    
    def get_input(self, prompt: str, required: bool = True) -> str:
        """Get input from user with validation"""
        while True:
            try:
                colored_prompt = Colors.colorize(prompt, Colors.SKY_BLUE)
                value = input(colored_prompt).strip()
                if required and not value:
                    print(Colors.colorize("This field is required. Please enter a value.", Colors.YELLOW))
                    continue
                return value
            except KeyboardInterrupt:
                print(Colors.colorize("\nOperation cancelled", Colors.RED))
                return ""
    
    def get_multiselect(self, prompt: str, options: List[str]) -> List[str]:
        """Get multiple selections from user"""
        print(f"\n{prompt}")
        print("Enter numbers separated by commas (e.g., 1,3):")
        
        for i, option in enumerate(options, 1):
            print(f"  [{i}] {option}")
        
        while True:
            try:
                selections = input("\nYour selection: ").strip()
                if not selections:
                    return []
                
                indices = [int(x.strip()) for x in selections.split(',')]
                selected = []
                
                for idx in indices:
                    if 1 <= idx <= len(options):
                        selected.append(options[idx - 1])
                    else:
                        print(f"Invalid selection: {idx}")
                        break
                else:
                    return selected
                    
            except ValueError:
                print("Please enter valid numbers separated by commas")
            except KeyboardInterrupt:
                print("\nSelection cancelled")
                return []

class HistoryManager:
    """Manage scan history with persistent storage"""
    
    def __init__(self):
        self.history_file = "scan_history.json"
        self.history = self.load_history()
    
    def load_history(self) -> List[Dict]:
        """Load scan history from file"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Warning: Could not load history: {e}")
        return []
    
    def save_history(self):
        """Save scan history to file"""
        try:
            with open(self.history_file, 'w') as f:
                json.dump(self.history, f, indent=2)
        except Exception as e:
            print(f"Warning: Could not save history: {e}")
    
    def add_detection(self, code: str, account_type: str, source: str):
        """Add new code detection to history"""
        detection = {
            'timestamp': datetime.now().isoformat(),
            'code': code,
            'account_type': account_type,
            'source': source,
            'validated': False
        }
        self.history.append(detection)
        self.save_history()
    
    def mark_validated(self, code: str):
        """Mark a code as validated"""
        for detection in reversed(self.history):
            if detection['code'] == code:
                detection['validated'] = True
                break
        self.save_history()
    
    def get_recent_history(self, limit: int = 20) -> List[Dict]:
        """Get recent detection history"""
        return self.history[-limit:] if self.history else []
    
    def show_history(self, console: ConsoleInterface):
        """Display scan history"""
        console.clear_screen()
        console.show_banner()
        print("=" * 80)
        print("                            SCAN HISTORY")
        print("=" * 80)
        
        recent = self.get_recent_history()
        if not recent:
            print("\nNo scan history available.")
        else:
            print(f"\nShowing last {len(recent)} detections:")
            print()
            for detection in reversed(recent):
                timestamp = datetime.fromisoformat(detection['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
                status = "✅ VALIDATED" if detection['validated'] else "⏳ PENDING"
                print(f"[{timestamp}] {detection['code']} - {detection['account_type']} - {status}")
        
        print("\n" + "=" * 80)
        input("Press Enter to continue...")

class TesseractScanner:
    """Tesseract OCR scanner for code detection"""

    def __init__(self):
        self.ocr_engine = console_ocr
        self.tesseract_available = is_ocr_available()

        if not self.tesseract_available:
            print("Error: Tesseract OCR not available!")
            print("Please install Tesseract OCR:")
            print("Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki")
            print("macOS: brew install tesseract")
            print("Linux: sudo apt install tesseract-ocr")

    def is_available(self) -> bool:
        """Check if scanner is available"""
        return self.tesseract_available

    def detect_codes_in_frame(self, frame, enabled_types: List[str]) -> List[Tuple[str, str]]:
        """Detect codes in video frame"""
        if not self.tesseract_available:
            return []

        return self.ocr_engine.detect_codes_in_frame(frame, enabled_types)

class ScannerStats:
    """Track scanning statistics"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        """Reset all statistics"""
        self.start_time = time.time()
        self.frames_processed = 0
        self.codes_detected = 0
        self.codes_validated = 0
        self.code_types = {'Starter': 0, 'Starter Plus': 0, 'Expert': 0}
    
    def increment_frames(self):
        """Increment frames processed"""
        self.frames_processed += 1
    
    def add_detection(self, account_type: str):
        """Add code detection"""
        self.codes_detected += 1
        if account_type in self.code_types:
            self.code_types[account_type] += 1
    
    def add_validation(self):
        """Add code validation"""
        self.codes_validated += 1
    
    def get_runtime(self) -> str:
        """Get formatted runtime"""
        elapsed = int(time.time() - self.start_time)
        hours = elapsed // 3600
        minutes = (elapsed % 3600) // 60
        seconds = elapsed % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    def get_fps(self) -> float:
        """Get frames per second"""
        elapsed = time.time() - self.start_time
        if elapsed > 0:
            return self.frames_processed / elapsed
        return 0.0

    def get_detection_rate(self) -> float:
        """Get detection rate in codes per minute"""
        elapsed = time.time() - self.start_time
        if elapsed > 0:
            return (self.codes_detected / elapsed) * 60
        return 0.0

def main():
    """Main application entry point"""
    console = ConsoleInterface()
    history = HistoryManager()
    scanner = TesseractScanner()

    if not OCR_AVAILABLE or not scanner.is_available():
        print(Colors.colorize("\nRequired dependencies not available:", Colors.RED))
        print(Colors.colorize("- Tesseract OCR", Colors.LIGHT_GRAY))
        print(Colors.colorize("- OpenCV (cv2)", Colors.LIGHT_GRAY))
        print(Colors.colorize("- NumPy", Colors.LIGHT_GRAY))
        print(Colors.colorize("\nPlease install missing dependencies and try again.", Colors.YELLOW))
        input("Press Enter to exit...")
        return
    
    while True:
        console.show_main_menu()
        choice = console.get_menu_choice(3)
        
        if choice == 1:
            # Real-Time Scanner
            run_realtime_scanner(console, history, scanner)
        elif choice == 2:
            # Test Scanner
            run_test_scanner(console, history, scanner)
        elif choice == 3:
            # Exit
            console.clear_screen()
            print(Colors.colorize("Thank you for using MFFUHijack Console Scanner!", Colors.SKY_BLUE))
            break

def run_realtime_scanner(console: ConsoleInterface, history: HistoryManager, scanner: TesseractScanner):
    """Run real-time scanner configuration and execution"""
    console.clear_screen()
    console.show_banner()
    print(Colors.colorize("=" * 80, Colors.SKY_BLUE))
    print(Colors.colorize("                         REAL-TIME SCANNER SETUP", Colors.WHITE))
    print(Colors.colorize("=" * 80, Colors.SKY_BLUE))

    # Get configuration
    print(f"\n{Colors.colorize('1. YouTube Livestream Configuration:', Colors.SKY_BLUE)}")
    while True:
        url = console.get_input("Enter YouTube livestream URL: ")
        if not url:
            return
        if validate_youtube_url(url):
            break
        print(Colors.colorize("Please enter a valid YouTube URL", Colors.YELLOW))

    print(f"\n{Colors.colorize('2. Account Type Selection:', Colors.SKY_BLUE)}")
    account_types = ['Starter', 'Starter Plus', 'Expert']
    selected_types = console.get_multiselect("Select account types to scan for:", account_types)
    if not selected_types:
        print(Colors.colorize("No account types selected. Returning to main menu.", Colors.YELLOW))
        input("Press Enter to continue...")
        return

    print("\n3. Account Credentials:")
    username = console.get_input("Username: ")

    print("\n4. Additional Options:")
    print("  [1] Start scanning now")
    print("  [2] View scan history")
    print("  [3] Return to main menu")

    option = console.get_menu_choice(3)

    if option == 1:
        start_realtime_scan(console, history, scanner, url, selected_types, username)
    elif option == 2:
        history.show_history(console)
        run_realtime_scanner(console, history, scanner)  # Return to config
    elif option == 3:
        return

def run_test_scanner(console: ConsoleInterface, history: HistoryManager, scanner: TesseractScanner):
    """Run test scanner configuration and execution"""
    console.clear_screen()
    console.show_banner()
    print("=" * 80)
    print("                           TEST SCANNER SETUP")
    print("=" * 80)

    # Create test_streams directory if it doesn't exist
    test_dir = "test_streams"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)

    print("\n1. Stream Selection:")
    print("  [1] Select existing stream file")
    print("  [2] Download new stream")
    print("  [3] Return to main menu")

    choice = console.get_menu_choice(3)

    if choice == 1:
        # Select existing file
        files = [f for f in os.listdir(test_dir) if f.endswith(('.mp4', '.mkv', '.avi', '.mov'))]
        if not files:
            print("No stream files found in test_streams directory.")
            input("Press Enter to continue...")
            return

        print("\nAvailable stream files:")
        for i, file in enumerate(files, 1):
            print(f"  [{i}] {file}")

        file_choice = console.get_menu_choice(len(files))
        selected_file = os.path.join(test_dir, files[file_choice - 1])

    elif choice == 2:
        # Download new stream
        url = console.get_input("Enter YouTube URL to download: ")
        if not url:
            return

        print("Downloading stream... (this may take a while)")
        # Note: Actual download implementation would use yt-dlp
        print("Download feature requires yt-dlp implementation")
        input("Press Enter to continue...")
        return

    elif choice == 3:
        return

    # Continue with test scanner configuration
    configure_test_scanner(console, history, scanner, selected_file)

def configure_test_scanner(console: ConsoleInterface, history: HistoryManager, scanner: TesseractScanner, video_file: str):
    """Configure test scanner parameters"""
    console.clear_screen()
    console.show_banner()
    print("=" * 80)
    print("                      TEST SCANNER CONFIGURATION")
    print("=" * 80)

    print(f"\nSelected file: {os.path.basename(video_file)}")

    # Get scan interval
    print("\n1. Scan Configuration:")
    while True:
        try:
            interval = float(console.get_input("Scan interval (seconds, default 5): ") or "5")
            if interval > 0:
                break
            print("Interval must be greater than 0")
        except ValueError:
            print("Please enter a valid number")

    # Get start time
    print("\n2. Start Time (HH:MM:SS format, default 00:00:00):")
    start_time = console.get_input("Start time: ") or "00:00:00"

    # Get account types
    print("\n3. Account Type Selection:")
    account_types = ['Starter', 'Starter Plus', 'Expert']
    selected_types = console.get_multiselect("Select account types to scan for:", account_types)
    if not selected_types:
        print("No account types selected. Returning to main menu.")
        input("Press Enter to continue...")
        return

    # Get credentials
    print("\n4. Account Credentials:")
    username = console.get_input("Username: ")

    # Start options
    print("\n5. Options:")
    print("  [1] Start test scan")
    print("  [2] View scan history")
    print("  [3] Return to main menu")

    option = console.get_menu_choice(3)

    if option == 1:
        start_test_scan(console, history, scanner, video_file, interval, start_time, selected_types, username)
    elif option == 2:
        history.show_history(console)
        configure_test_scanner(console, history, scanner, video_file)
    elif option == 3:
        return

def start_realtime_scan(console: ConsoleInterface, history: HistoryManager, scanner: TesseractScanner,
                       url: str, account_types: List[str], username: str):
    """Start real-time scanning with actual livestream capture"""
    console.clear_screen()
    console.show_banner()

    stats = ScannerStats()
    print(Colors.colorize("=" * 80, Colors.SKY_BLUE))
    print(Colors.colorize("                         REAL-TIME SCANNER ACTIVE", Colors.WHITE))
    print(Colors.colorize("=" * 80, Colors.SKY_BLUE))
    print(f"\n{Colors.colorize('Stream:', Colors.SKY_BLUE)} {Colors.colorize(url, Colors.LIGHT_GRAY)}")
    print(f"{Colors.colorize('Account Types:', Colors.SKY_BLUE)} {Colors.colorize(', '.join(account_types), Colors.LIGHT_GRAY)}")
    print(f"{Colors.colorize('Username:', Colors.SKY_BLUE)} {Colors.colorize(username, Colors.LIGHT_GRAY)}")
    print(f"\n{Colors.colorize('Press Ctrl+C to stop scanning', Colors.YELLOW)}")
    print(Colors.colorize("=" * 80, Colors.SKY_BLUE))

    if not YTDLP_AVAILABLE:
        print(Colors.colorize("\n⚠️ yt-dlp not available for livestream capture!", Colors.YELLOW))
        print(Colors.colorize("Would you like to run in demo mode instead?", Colors.SKY_BLUE))
        choice = input(Colors.colorize("Demo mode (Y/N): ", Colors.SKY_BLUE)).strip().upper()
        if choice in ['Y', 'YES']:
            run_demo_scanner(console, history, url, account_types, username)
            return
        else:
            print(Colors.colorize("Please install yt-dlp: pip install yt-dlp", Colors.YELLOW))
            input("Press Enter to continue...")
            return

    try:
        # Setup yt-dlp for livestream capture
        print(f"\n{Colors.colorize('🌐 Connecting to livestream...', Colors.SKY_BLUE)}")
        print(Colors.colorize(f"📺 URL: {url}", Colors.LIGHT_GRAY))

        ydl_opts = {
            'format': 'best[height<=720]',  # Limit to 720p for performance
            'quiet': True,
            'no_warnings': True,
        }

        print(Colors.colorize("🔍 Extracting stream information...", Colors.SKY_BLUE))

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # Get stream info
            try:
                info = ydl.extract_info(url, download=False)
            except Exception as e:
                print(Colors.colorize(f"❌ Failed to extract stream info: {str(e)}", Colors.RED))
                print(Colors.colorize("💡 Make sure the URL is a valid YouTube livestream", Colors.YELLOW))
                input("Press Enter to continue...")
                return

            if not info:
                print(Colors.colorize("❌ No stream information found", Colors.RED))
                print(Colors.colorize("💡 The stream might be offline or the URL is incorrect", Colors.YELLOW))
                input("Press Enter to continue...")
                return

            print(Colors.colorize(f"✅ Stream info extracted: {info.get('title', 'Unknown')}", Colors.GREEN))

            # Get the actual stream URL
            stream_url = None
            if 'url' in info:
                stream_url = info['url']
            elif 'formats' in info and info['formats']:
                print(Colors.colorize("🔍 Finding best video format...", Colors.SKY_BLUE))
                # Find best format
                for fmt in info['formats']:
                    if fmt.get('url') and fmt.get('vcodec') != 'none':
                        stream_url = fmt['url']
                        print(Colors.colorize(f"✅ Found format: {fmt.get('format_note', 'Unknown')} - {fmt.get('ext', 'Unknown')}", Colors.GREEN))
                        break

            if not stream_url:
                print(Colors.colorize("❌ No suitable video stream URL found", Colors.RED))
                print(Colors.colorize("💡 The stream might not have video or is not accessible", Colors.YELLOW))
                input("Press Enter to continue...")
                return

            print(Colors.colorize("✅ Stream URL obtained successfully", Colors.GREEN))
            print(Colors.colorize("🎥 Initializing video capture...", Colors.SKY_BLUE))

            # Open video capture
            cap = cv2.VideoCapture(stream_url)
            if not cap.isOpened():
                print(Colors.colorize("❌ Failed to open video stream", Colors.RED))
                print(Colors.colorize("💡 The stream might be offline or require authentication", Colors.YELLOW))
                input("Press Enter to continue...")
                return

            print(Colors.colorize("✅ Video capture initialized", Colors.GREEN))
            print(Colors.colorize("\nStarting OCR scanning...", Colors.SKY_BLUE))

            last_scan_time = time.time()
            last_display_update = time.time()
            scan_interval = 2.0  # Scan every 2 seconds
            display_update_interval = 1.0  # Update display every second

            # Initial dashboard display
            console.clear_screen()
            console.show_banner()
            show_scan_dashboard(stats, account_types)
            print(Colors.colorize(f"\n🔍 Scanning stream: {url}", Colors.SKY_BLUE))
            print(Colors.colorize("📊 Dashboard updates every second | OCR scans every 2 seconds", Colors.LIGHT_GRAY))

            while True:
                ret, frame = cap.read()
                if not ret:
                    print(Colors.colorize("\n⚠️ Stream ended or connection lost", Colors.YELLOW))
                    break

                stats.increment_frames()
                current_time = time.time()

                # Perform OCR scan at intervals
                if current_time - last_scan_time >= scan_interval:
                    print(Colors.colorize(f"\n🔍 Performing OCR scan on frame {stats.frames_processed}...", Colors.SKY_BLUE))

                    try:
                        detected_codes = scanner.detect_codes_in_frame(frame, account_types)

                        if detected_codes:
                            for code, account_type in detected_codes:
                                stats.add_detection(account_type)
                                history.add_detection(code, account_type, url)

                                # Immediate update for code detection
                                console.clear_screen()
                                console.show_banner()
                                show_scan_dashboard(stats, account_types)

                                timestamp = datetime.now().strftime('%H:%M:%S')
                                print(f"\n{Colors.colorize('🎉 CODE DETECTED!', Colors.GREEN)}")
                                print(f"{Colors.colorize('[' + timestamp + ']', Colors.SKY_BLUE)} {Colors.colorize('Code:', Colors.WHITE)} {Colors.colorize(code, Colors.GREEN)} - {Colors.colorize('Type:', Colors.WHITE)} {Colors.colorize(account_type, Colors.GREEN)}")
                        else:
                            print(Colors.colorize("   No codes detected in this frame", Colors.LIGHT_GRAY))
                    except Exception as ocr_error:
                        print(Colors.colorize(f"   OCR processing error: {ocr_error}", Colors.RED))

                    last_scan_time = current_time

                # Update display regularly (every second)
                if current_time - last_display_update >= display_update_interval:
                    console.clear_screen()
                    console.show_banner()
                    show_scan_dashboard(stats, account_types)

                    # Show current status
                    print(Colors.colorize(f"\n🔍 Scanning stream: {url}", Colors.SKY_BLUE))
                    print(Colors.colorize(f"📊 Frame: {stats.frames_processed} | FPS: {stats.get_fps():.1f} | Runtime: {stats.get_runtime()}", Colors.LIGHT_GRAY))

                    # Show time until next scan
                    time_until_scan = scan_interval - (current_time - last_scan_time)
                    if time_until_scan > 0:
                        print(Colors.colorize(f"⏱️  Next OCR scan in: {time_until_scan:.1f}s", Colors.YELLOW))
                    else:
                        print(Colors.colorize("🔍 OCR scan in progress...", Colors.SKY_BLUE))

                    last_display_update = current_time

            cap.release()

    except KeyboardInterrupt:
        print(Colors.colorize("\n\n🛑 Real-time scanning stopped by user.", Colors.YELLOW))
    except Exception as e:
        print(Colors.colorize(f"\n❌ Error during real-time scanning: {e}", Colors.RED))
    finally:
        # Ensure video capture is always released
        if 'cap' in locals() and cap is not None:
            cap.release()
        input("Press Enter to continue...")

def run_demo_scanner(console: ConsoleInterface, history: HistoryManager,
                    url: str, account_types: List[str], username: str):
    """Run demo scanner that simulates real scanning with realistic updates"""
    console.clear_screen()
    console.show_banner()

    stats = ScannerStats()
    print(Colors.colorize("=" * 80, Colors.SKY_BLUE))
    print(Colors.colorize("                         DEMO SCANNER ACTIVE", Colors.WHITE))
    print(Colors.colorize("=" * 80, Colors.SKY_BLUE))
    print(f"\n{Colors.colorize('Stream:', Colors.SKY_BLUE)} {Colors.colorize(url, Colors.LIGHT_GRAY)}")
    print(f"{Colors.colorize('Account Types:', Colors.SKY_BLUE)} {Colors.colorize(', '.join(account_types), Colors.LIGHT_GRAY)}")
    print(f"{Colors.colorize('Username:', Colors.SKY_BLUE)} {Colors.colorize(username, Colors.LIGHT_GRAY)}")
    print(f"\n{Colors.colorize('🎭 DEMO MODE - Simulating real livestream scanning', Colors.YELLOW)}")
    print(f"{Colors.colorize('Press Ctrl+C to stop scanning', Colors.YELLOW)}")
    print(Colors.colorize("=" * 80, Colors.SKY_BLUE))

    try:
        last_display_update = time.time()
        last_scan_time = time.time()
        display_update_interval = 1.0
        scan_interval = 3.0  # Demo scans every 3 seconds

        # Initial dashboard display
        console.clear_screen()
        console.show_banner()
        show_scan_dashboard(stats, account_types)
        print(Colors.colorize(f"\n🎭 Demo scanning: {url}", Colors.YELLOW))
        print(Colors.colorize("📊 Dashboard updates every second | Demo OCR scans every 3 seconds", Colors.LIGHT_GRAY))

        while True:
            # Simulate frame processing
            stats.increment_frames()
            current_time = time.time()

            # Simulate OCR scanning
            if current_time - last_scan_time >= scan_interval:
                print(Colors.colorize(f"\n🔍 Performing demo OCR scan on frame {stats.frames_processed}...", Colors.SKY_BLUE))

                # Simulate occasional code detection (20% chance)
                if random.random() < 0.2:
                    # Generate demo code
                    demo_codes = ["DEMO123", "TEST456", "SCAN789", "CODE999", "LIVE555"]
                    code = random.choice(demo_codes)
                    account_type = random.choice(account_types)

                    stats.add_detection(account_type)
                    history.add_detection(code, account_type, url)

                    # Immediate update for code detection
                    console.clear_screen()
                    console.show_banner()
                    show_scan_dashboard(stats, account_types)

                    timestamp = datetime.now().strftime('%H:%M:%S')
                    print(f"\n{Colors.colorize('🎉 DEMO CODE DETECTED!', Colors.GREEN)}")
                    print(f"{Colors.colorize('[' + timestamp + ']', Colors.SKY_BLUE)} {Colors.colorize('Code:', Colors.WHITE)} {Colors.colorize(code, Colors.GREEN)} - {Colors.colorize('Type:', Colors.WHITE)} {Colors.colorize(account_type, Colors.GREEN)}")
                    print(Colors.colorize("🎭 This is a demo detection - not a real code", Colors.YELLOW))
                else:
                    print(Colors.colorize("   No codes detected in this demo frame", Colors.LIGHT_GRAY))

                last_scan_time = current_time

            # Update display regularly
            if current_time - last_display_update >= display_update_interval:
                console.clear_screen()
                console.show_banner()
                show_scan_dashboard(stats, account_types)

                # Show current status
                print(Colors.colorize(f"\n🎭 Demo scanning: {url}", Colors.YELLOW))
                print(Colors.colorize(f"📊 Frame: {stats.frames_processed} | FPS: {stats.get_fps():.1f} | Runtime: {stats.get_runtime()}", Colors.LIGHT_GRAY))

                # Show time until next scan
                time_until_scan = scan_interval - (current_time - last_scan_time)
                if time_until_scan > 0:
                    print(Colors.colorize(f"⏱️  Next demo OCR scan in: {time_until_scan:.1f}s", Colors.YELLOW))
                else:
                    print(Colors.colorize("🔍 Demo OCR scan in progress...", Colors.SKY_BLUE))

                last_display_update = current_time

            time.sleep(0.1)  # Small delay to prevent excessive CPU usage

    except KeyboardInterrupt:
        print(Colors.colorize("\n\n🛑 Demo scanning stopped by user.", Colors.YELLOW))
        input("Press Enter to continue...")

def start_test_scan(console: ConsoleInterface, history: HistoryManager, scanner: TesseractScanner,
                   video_file: str, interval: float, start_time: str, account_types: List[str],
                   username: str):
    """Start test scanning with real-time updates"""
    console.clear_screen()
    console.show_banner()

    stats = ScannerStats()
    print(Colors.colorize("=" * 80, Colors.SKY_BLUE))
    print(Colors.colorize("                           TEST SCANNER ACTIVE", Colors.WHITE))
    print(Colors.colorize("=" * 80, Colors.SKY_BLUE))
    print(f"\n{Colors.colorize('File:', Colors.SKY_BLUE)} {Colors.colorize(os.path.basename(video_file), Colors.LIGHT_GRAY)}")
    print(f"{Colors.colorize('Scan Interval:', Colors.SKY_BLUE)} {Colors.colorize(f'{interval}s', Colors.LIGHT_GRAY)}")
    print(f"{Colors.colorize('Start Time:', Colors.SKY_BLUE)} {Colors.colorize(start_time, Colors.LIGHT_GRAY)}")
    print(f"{Colors.colorize('Account Types:', Colors.SKY_BLUE)} {Colors.colorize(', '.join(account_types), Colors.LIGHT_GRAY)}")
    print(f"{Colors.colorize('Username:', Colors.SKY_BLUE)} {Colors.colorize(username, Colors.LIGHT_GRAY)}")
    print(f"\n{Colors.colorize('Press Ctrl+C to stop scanning', Colors.YELLOW)}")
    print(Colors.colorize("=" * 80, Colors.SKY_BLUE))

    if not OCR_AVAILABLE:
        print(Colors.colorize("\n❌ Video processing not available!", Colors.RED))
        print(Colors.colorize("💡 Please install opencv-python: pip install opencv-python", Colors.YELLOW))
        input("Press Enter to continue...")
        return

    try:
        # Open video file
        print(f"\n{Colors.colorize('📁 Opening video file...', Colors.SKY_BLUE)}")
        print(Colors.colorize(f"📺 File: {video_file}", Colors.LIGHT_GRAY))

        cap = cv2.VideoCapture(video_file)
        if not cap.isOpened():
            print(Colors.colorize(f"❌ Could not open video file: {os.path.basename(video_file)}", Colors.RED))
            print(Colors.colorize("💡 Make sure the file exists and is a valid video format", Colors.YELLOW))
            input("Press Enter to continue...")
            return

        print(Colors.colorize("✅ Video file opened successfully", Colors.GREEN))

        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration_seconds = total_frames / fps if fps > 0 else 0
        duration_str = f"{int(duration_seconds // 3600):02d}:{int((duration_seconds % 3600) // 60):02d}:{int(duration_seconds % 60):02d}"

        print(Colors.colorize("📊 Analyzing video properties...", Colors.SKY_BLUE))
        print(Colors.colorize(f"   Duration: {duration_str} ({total_frames} frames)", Colors.LIGHT_GRAY))
        print(Colors.colorize(f"   FPS: {fps:.2f}", Colors.LIGHT_GRAY))

        # Parse start time and calculate start frame
        start_frame = parse_time_to_frame(start_time, fps)
        if start_frame > 0:
            print(Colors.colorize(f"⏭️  Seeking to start time: {start_time} (frame {start_frame})", Colors.SKY_BLUE))
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
        else:
            print(Colors.colorize("▶️  Starting from beginning of video", Colors.SKY_BLUE))

        # Calculate frame interval
        frame_interval = int(fps * interval)
        print(Colors.colorize(f"🔍 Scan settings: Every {interval}s ({frame_interval} frames)", Colors.SKY_BLUE))
        print(Colors.colorize("✅ Video analysis complete - starting scan...", Colors.GREEN))

        last_scan_time = time.time()
        last_display_update = time.time()
        display_update_interval = 5.0  # Update display every 5 seconds (less screen clearing)
        frame_count = 0

        # Initial dashboard display
        console.clear_screen()
        console.show_banner()
        show_scan_dashboard(stats, account_types)
        print(Colors.colorize(f"\n🎬 Testing video: {os.path.basename(video_file)}", Colors.SKY_BLUE))
        print(Colors.colorize("📊 Dashboard updates every 5 seconds | OCR scans based on interval", Colors.LIGHT_GRAY))
        print(Colors.colorize("🚀 Starting video processing...", Colors.GREEN))

        while True:
            ret, frame = cap.read()
            if not ret:
                print(Colors.colorize("\n🏁 End of video reached.", Colors.GREEN))
                break

            stats.increment_frames()
            frame_count += 1
            current_time = time.time()

            # Show progress every 1000 frames to indicate it's working
            if frame_count % 1000 == 0:
                current_frame = int(cap.get(cv2.CAP_PROP_POS_FRAMES))
                progress_percent = (current_frame / total_frames * 100) if total_frames > 0 else 0
                print(Colors.colorize(f"📊 Processing... Frame {current_frame}/{total_frames} ({progress_percent:.1f}%)", Colors.LIGHT_GRAY))

            # Check if it's time to scan
            if current_time - last_scan_time >= interval:
                # Get current video position for display
                current_frame = int(cap.get(cv2.CAP_PROP_POS_FRAMES))
                current_seconds = current_frame / fps if fps > 0 else 0
                current_time_str = f"{int(current_seconds // 3600):02d}:{int((current_seconds % 3600) // 60):02d}:{int(current_seconds % 60):02d}"

                print(Colors.colorize(f"\n🔍 Performing OCR scan at {current_time_str} (frame {current_frame})...", Colors.SKY_BLUE))

                # Detect codes in current frame
                try:
                    detected_codes = scanner.detect_codes_in_frame(frame, account_types)

                    if detected_codes:
                        for code, account_type in detected_codes:
                            stats.add_detection(account_type)
                            history.add_detection(code, account_type, os.path.basename(video_file))

                            # Immediate update for code detection
                            console.clear_screen()
                            console.show_banner()
                            show_scan_dashboard(stats, account_types)

                            timestamp = datetime.now().strftime('%H:%M:%S')
                            print(f"\n{Colors.colorize('🎉 CODE DETECTED IN TEST VIDEO!', Colors.GREEN)}")
                            print(f"{Colors.colorize('[' + timestamp + ']', Colors.SKY_BLUE)} {Colors.colorize('Code:', Colors.WHITE)} {Colors.colorize(code, Colors.GREEN)} - {Colors.colorize('Type:', Colors.WHITE)} {Colors.colorize(account_type, Colors.GREEN)}")
                            print(Colors.colorize(f"📍 Video Position: {current_time_str} (frame {current_frame})", Colors.LIGHT_GRAY))
                    else:
                        print(Colors.colorize("   No codes detected in this frame", Colors.LIGHT_GRAY))
                except Exception as ocr_error:
                    print(Colors.colorize(f"   OCR processing error: {ocr_error}", Colors.RED))

                last_scan_time = current_time

            # Update display regularly (every second)
            if current_time - last_display_update >= display_update_interval:
                console.clear_screen()
                console.show_banner()
                show_scan_dashboard(stats, account_types)

                # Show current video position
                current_frame = int(cap.get(cv2.CAP_PROP_POS_FRAMES))
                current_seconds = current_frame / fps if fps > 0 else 0
                current_time_str = f"{int(current_seconds // 3600):02d}:{int((current_seconds % 3600) // 60):02d}:{int(current_seconds % 60):02d}"
                progress_percent = (current_frame / total_frames * 100) if total_frames > 0 else 0

                print(Colors.colorize(f"\n🎬 Testing video: {os.path.basename(video_file)}", Colors.SKY_BLUE))
                print(Colors.colorize(f"📍 Position: {current_time_str} | Frame: {current_frame}/{total_frames} ({progress_percent:.1f}%)", Colors.LIGHT_GRAY))

                # Show time until next scan
                time_until_scan = interval - (current_time - last_scan_time)
                if time_until_scan > 0:
                    print(Colors.colorize(f"⏱️  Next OCR scan in: {time_until_scan:.1f}s", Colors.YELLOW))
                else:
                    print(Colors.colorize("🔍 OCR scan in progress...", Colors.SKY_BLUE))

                last_display_update = current_time

            # Skip frames to maintain interval (optimized for performance)
            frames_to_skip = frame_interval - 1
            for _ in range(frames_to_skip):
                ret, _ = cap.read()
                if not ret:
                    break
                stats.increment_frames()

        cap.release()
        print(Colors.colorize(f"\n✅ Test scanning completed successfully!", Colors.GREEN))
        print(Colors.colorize(f"📊 Final Results: {stats.codes_detected} codes detected in {stats.get_runtime()}", Colors.WHITE))

    except KeyboardInterrupt:
        print(Colors.colorize("\n\n🛑 Test scanning stopped by user.", Colors.YELLOW))
    except Exception as e:
        print(Colors.colorize(f"\n❌ Error during test scanning: {e}", Colors.RED))
    finally:
        # Ensure video capture is always released
        if 'cap' in locals() and cap is not None:
            cap.release()
        input("Press Enter to continue...")

def parse_time_to_frame(time_str: str, fps: float) -> int:
    """Parse HH:MM:SS time string to frame number with validation"""
    try:
        if not time_str or not time_str.strip():
            return 0

        parts = time_str.strip().split(':')
        if len(parts) != 3:
            return 0

        hours = int(parts[0])
        minutes = int(parts[1])
        seconds = int(parts[2])

        # Validate time components
        if hours < 0 or minutes < 0 or minutes >= 60 or seconds < 0 or seconds >= 60:
            return 0

        total_seconds = hours * 3600 + minutes * 60 + seconds
        return int(total_seconds * fps) if fps > 0 else 0
    except (ValueError, IndexError, TypeError):
        return 0

def validate_youtube_url(url: str) -> bool:
    """Validate if URL is a YouTube URL"""
    if not url or not url.strip():
        return False

    url = url.strip().lower()
    youtube_domains = ['youtube.com', 'youtu.be', 'www.youtube.com', 'm.youtube.com']
    return any(domain in url for domain in youtube_domains)

def show_scan_dashboard(stats: ScannerStats, account_types: List[str]):
    """Display real-time scanning dashboard with colors"""
    print(Colors.colorize("=" * 80, Colors.SKY_BLUE))
    print(Colors.colorize("                           SCANNING DASHBOARD", Colors.WHITE))
    print(Colors.colorize("=" * 80, Colors.SKY_BLUE))

    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    runtime = stats.get_runtime()

    print(f"\n{Colors.colorize('Current Time:', Colors.SKY_BLUE)}      {Colors.colorize(current_time, Colors.LIGHT_GRAY)}")
    print(f"{Colors.colorize('Runtime:', Colors.SKY_BLUE)}           {Colors.colorize(runtime, Colors.LIGHT_GRAY)}")
    print(f"{Colors.colorize('Frames Processed:', Colors.SKY_BLUE)}  {Colors.colorize(str(stats.frames_processed), Colors.WHITE)}")
    print(f"{Colors.colorize('Codes Detected:', Colors.SKY_BLUE)}    {Colors.colorize(str(stats.codes_detected), Colors.GREEN if stats.codes_detected > 0 else Colors.LIGHT_GRAY)}")
    print(f"{Colors.colorize('Codes Validated:', Colors.SKY_BLUE)}   {Colors.colorize(str(stats.codes_validated), Colors.GREEN if stats.codes_validated > 0 else Colors.LIGHT_GRAY)}")

    print(f"\n{Colors.colorize('Code Types Found:', Colors.SKY_BLUE)}")
    for account_type in account_types:
        count = stats.code_types.get(account_type, 0)
        count_color = Colors.GREEN if count > 0 else Colors.LIGHT_GRAY
        print(f"  {Colors.colorize(account_type + ':', Colors.LIGHT_GRAY)} {Colors.colorize(str(count), count_color)}")

    fps = stats.get_fps()
    detection_rate = stats.get_detection_rate()
    print(f"\n{Colors.colorize('FPS:', Colors.SKY_BLUE)} {Colors.colorize(f'{fps:.1f}', Colors.WHITE)}")
    print(f"{Colors.colorize('Detection Rate:', Colors.SKY_BLUE)} {Colors.colorize(f'{detection_rate:.2f} codes/min', Colors.WHITE)}")
    print("\n" + Colors.colorize("=" * 80, Colors.SKY_BLUE))

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nApplication terminated by user.")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        input("Press Enter to exit...")
