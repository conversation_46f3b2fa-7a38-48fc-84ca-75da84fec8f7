#!/usr/bin/env python3
"""
Test script to verify the new stream testing flow:
1. "Start Testing" button only opens preview window
2. Preview window "Start Scanning" button actually starts OCR scanning
3. Scanning only works when video is playing
"""

import sys
from pathlib import Path

# Add support directory to path
sys.path.append(str(Path(__file__).parent / "MFFUHijack_Support"))

def test_preview_window_parameters():
    """Test that StreamPreviewWindow has the new testing parameters"""
    print("🧪 Testing Preview Window Parameters...")
    print("=" * 40)
    
    try:
        # Read the main file and check for new parameters
        main_file = Path("mffuhijack_main.py")
        if not main_file.exists():
            print("⚠️  Main file not found")
            return False
            
        content = main_file.read_text(encoding='utf-8')
        
        # Check for new parameters in StreamPreviewWindow
        parameters = [
            "self.stream_path = None",
            "self.frame_interval = 5.0",
            "self.ocr_engine = \"easyocr\"",
            "self.validate_codes = False",
            "self.start_time_seconds = 0.0",
            "self.parent_tab = None",
            "self.testing_worker = None",
            "self.is_scanning = False"
        ]
        
        found = 0
        for param in parameters:
            if param in content:
                print(f"✅ Found parameter: {param}")
                found += 1
            else:
                print(f"❌ Missing parameter: {param}")
        
        if found >= 6:
            print(f"✅ Preview window parameters added ({found}/8)")
            return True
        else:
            print(f"❌ Missing preview window parameters ({found}/8)")
            return False
            
    except Exception as e:
        print(f"❌ Error testing preview window parameters: {e}")
        return False

def test_set_testing_parameters_method():
    """Test that set_testing_parameters method exists"""
    print("\n🧪 Testing set_testing_parameters Method...")
    print("=" * 40)
    
    try:
        # Read the main file and check for the method
        main_file = Path("mffuhijack_main.py")
        if not main_file.exists():
            print("⚠️  Main file not found")
            return False
            
        content = main_file.read_text(encoding='utf-8')
        
        # Check for the method definition
        if "def set_testing_parameters(self, stream_path, frame_interval, ocr_engine, ocr_region, validate_codes, start_time_seconds, parent_tab):" in content:
            print("✅ set_testing_parameters method found")
            
            # Check for parameter assignments
            assignments = [
                "self.stream_path = stream_path",
                "self.frame_interval = frame_interval",
                "self.ocr_engine = ocr_engine",
                "self.parent_tab = parent_tab"
            ]
            
            found_assignments = 0
            for assignment in assignments:
                if assignment in content:
                    found_assignments += 1
            
            if found_assignments >= 3:
                print(f"✅ Parameter assignments found ({found_assignments}/4)")
                return True
            else:
                print(f"❌ Missing parameter assignments ({found_assignments}/4)")
                return False
        else:
            print("❌ set_testing_parameters method not found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing set_testing_parameters method: {e}")
        return False

def test_modified_start_testing():
    """Test that start_testing method was modified to only open preview"""
    print("\n🧪 Testing Modified start_testing Method...")
    print("=" * 40)
    
    try:
        # Read the main file and check for modifications
        main_file = Path("mffuhijack_main.py")
        if not main_file.exists():
            print("⚠️  Main file not found")
            return False
            
        content = main_file.read_text(encoding='utf-8')
        
        # Check that it no longer creates StreamTestingWorker immediately
        if "# Open stream preview window (do not start scanning yet)" in content:
            print("✅ Found comment indicating preview-only behavior")
            
            # Check that it calls set_testing_parameters
            if "self.preview_window.set_testing_parameters(" in content:
                print("✅ Found call to set_testing_parameters")
                
                # Check that it doesn't immediately start worker
                if "StreamTestingWorker(" not in content.split("def start_testing(self):")[1].split("def stop_testing(self):")[0]:
                    print("✅ StreamTestingWorker not created in start_testing method")
                    return True
                else:
                    print("❌ StreamTestingWorker still being created in start_testing")
                    return False
            else:
                print("❌ set_testing_parameters call not found")
                return False
        else:
            print("❌ Preview-only comment not found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing modified start_testing: {e}")
        return False

def test_modified_toggle_interval_scanning():
    """Test that toggle_interval_scanning now starts actual OCR scanning"""
    print("\n🧪 Testing Modified toggle_interval_scanning Method...")
    print("=" * 40)
    
    try:
        # Read the main file and check for modifications
        main_file = Path("mffuhijack_main.py")
        if not main_file.exists():
            print("⚠️  Main file not found")
            return False
            
        content = main_file.read_text(encoding='utf-8')
        
        # Check for video playing check
        if "if not self.is_playing:" in content:
            print("✅ Found video playing check")
            
            # Check for StreamTestingWorker creation
            if "self.testing_worker = StreamTestingWorker(" in content:
                print("✅ Found StreamTestingWorker creation in toggle method")
                
                # Check for scanning state management
                if "self.is_scanning = True" in content and "self.is_scanning = False" in content:
                    print("✅ Found scanning state management")
                    
                    # Check for video playing requirement message
                    if "video is not playing" in content:
                        print("✅ Found video playing requirement message")
                        return True
                    else:
                        print("❌ Video playing requirement message not found")
                        return False
                else:
                    print("❌ Scanning state management not found")
                    return False
            else:
                print("❌ StreamTestingWorker creation not found in toggle method")
                return False
        else:
            print("❌ Video playing check not found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing modified toggle_interval_scanning: {e}")
        return False

def main():
    """Run all stream testing flow tests"""
    print("🚀 Stream Testing Flow Modification Test")
    print("=" * 60)
    
    tests = [
        ("Preview Window Parameters", test_preview_window_parameters),
        ("set_testing_parameters Method", test_set_testing_parameters_method),
        ("Modified start_testing Method", test_modified_start_testing),
        ("Modified toggle_interval_scanning", test_modified_toggle_interval_scanning)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}: EXCEPTION - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All stream testing flow modifications verified!")
        print("\n✅ New Flow:")
        print("   1. 'Start Testing' button → Opens preview window only")
        print("   2. Preview window 'Start Scanning' → Actually starts OCR scanning")
        print("   3. Scanning only works when video is playing")
        print("   4. Preview window manages its own testing worker")
        print("   5. Stop button works from both main tab and preview window")
        print("\n🚀 Stream testing flow should now work as requested!")
        return True
    else:
        print("⚠️  Some modifications may be incomplete. Check the output above.")
        return False

if __name__ == "__main__":
    main()
