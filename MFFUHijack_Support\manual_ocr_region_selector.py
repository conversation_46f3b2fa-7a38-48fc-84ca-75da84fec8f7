"""
Manual OCR Region Selector with Resizable Rectangle
Allows users to manually select and resize OCR regions over a template preview
"""

import sys
import os
import cv2
import numpy as np
from typing import Tuple, Optional
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *


class ResizableRectangle(QWidget):
    """Resizable rectangle widget for OCR region selection"""
    
    # Signals
    region_changed = pyqtSignal(int, int, int, int)  # x, y, width, height
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMouseTracking(True)
        
        # Rectangle properties
        self.selection_rect = QRect(50, 50, 200, 100)  # Default rectangle
        self.min_size = QSize(20, 20)
        self.handle_size = 8
        
        # Interaction state
        self.dragging = False
        self.resizing = False
        self.resize_handle = None
        self.drag_start_pos = QPoint()
        self.drag_start_rect = QRect()
        
        # Visual properties
        self.border_color = QColor(255, 0, 0, 200)  # Red border
        self.fill_color = QColor(255, 0, 0, 50)     # Semi-transparent red fill
        self.handle_color = QColor(255, 255, 255, 200)  # White handles
        
        self.setMinimumSize(400, 300)
    
    def set_rectangle(self, x: int, y: int, width: int, height: int):
        """Set rectangle position and size"""
        self.selection_rect = QRect(x, y, width, height)
        self.update()
        self.region_changed.emit(x, y, width, height)

    def get_rectangle(self) -> Tuple[int, int, int, int]:
        """Get rectangle position and size"""
        return (self.selection_rect.x(), self.selection_rect.y(), self.selection_rect.width(), self.selection_rect.height())
    
    def get_relative_rectangle(self) -> Tuple[float, float, float, float]:
        """Get rectangle as percentages of widget size"""
        widget_size = self.size()
        if widget_size.width() == 0 or widget_size.height() == 0:
            return (0.0, 0.0, 100.0, 100.0)
        
        x_percent = (self.selection_rect.x() / widget_size.width()) * 100
        y_percent = (self.selection_rect.y() / widget_size.height()) * 100
        w_percent = (self.selection_rect.width() / widget_size.width()) * 100
        h_percent = (self.selection_rect.height() / widget_size.height()) * 100
        
        return (x_percent, y_percent, w_percent, h_percent)
    
    def set_relative_rectangle(self, x_percent: float, y_percent: float, 
                              w_percent: float, h_percent: float):
        """Set rectangle from percentages of widget size"""
        widget_size = self.size()
        
        x = int((x_percent / 100) * widget_size.width())
        y = int((y_percent / 100) * widget_size.height())
        width = int((w_percent / 100) * widget_size.width())
        height = int((h_percent / 100) * widget_size.height())
        
        self.set_rectangle(x, y, width, height)
    
    def get_resize_handle(self, pos: QPoint) -> Optional[str]:
        """Get which resize handle is at the given position"""
        handles = self.get_handle_rects()
        
        for handle_name, handle_rect in handles.items():
            if handle_rect.contains(pos):
                return handle_name
        
        return None
    
    def get_handle_rects(self) -> dict:
        """Get rectangles for all resize handles"""
        h = self.handle_size
        rect = self.selection_rect
        
        return {
            'top_left': QRect(rect.left() - h//2, rect.top() - h//2, h, h),
            'top_right': QRect(rect.right() - h//2, rect.top() - h//2, h, h),
            'bottom_left': QRect(rect.left() - h//2, rect.bottom() - h//2, h, h),
            'bottom_right': QRect(rect.right() - h//2, rect.bottom() - h//2, h, h),
            'top': QRect(rect.center().x() - h//2, rect.top() - h//2, h, h),
            'bottom': QRect(rect.center().x() - h//2, rect.bottom() - h//2, h, h),
            'left': QRect(rect.left() - h//2, rect.center().y() - h//2, h, h),
            'right': QRect(rect.right() - h//2, rect.center().y() - h//2, h, h)
        }
    
    def get_cursor_for_handle(self, handle: str) -> Qt.CursorShape:
        """Get appropriate cursor for resize handle"""
        cursors = {
            'top_left': Qt.CursorShape.SizeFDiagCursor,
            'top_right': Qt.CursorShape.SizeBDiagCursor,
            'bottom_left': Qt.CursorShape.SizeBDiagCursor,
            'bottom_right': Qt.CursorShape.SizeFDiagCursor,
            'top': Qt.CursorShape.SizeVerCursor,
            'bottom': Qt.CursorShape.SizeVerCursor,
            'left': Qt.CursorShape.SizeHorCursor,
            'right': Qt.CursorShape.SizeHorCursor
        }
        return cursors.get(handle, Qt.CursorShape.ArrowCursor)
    
    def mousePressEvent(self, event):
        """Handle mouse press events"""
        if event.button() == Qt.MouseButton.LeftButton:
            pos = event.pos()
            
            # Check if clicking on a resize handle
            handle = self.get_resize_handle(pos)
            if handle:
                self.resizing = True
                self.resize_handle = handle
                self.drag_start_pos = pos
                self.drag_start_rect = QRect(self.selection_rect)
                self.setCursor(self.get_cursor_for_handle(handle))
            elif self.selection_rect.contains(pos):
                # Start dragging the rectangle
                self.dragging = True
                self.drag_start_pos = pos
                self.drag_start_rect = QRect(self.selection_rect)
                self.setCursor(Qt.CursorShape.SizeAllCursor)
    
    def mouseMoveEvent(self, event):
        """Handle mouse move events"""
        pos = event.pos()
        
        if self.resizing and self.resize_handle:
            # Resize the rectangle
            self.resize_rectangle(pos)
        elif self.dragging:
            # Move the rectangle
            delta = pos - self.drag_start_pos
            new_rect = QRect(self.drag_start_rect)
            new_rect.translate(delta)
            
            # Keep rectangle within widget bounds
            widget_rect = self.rect()
            if new_rect.left() < 0:
                new_rect.moveLeft(0)
            if new_rect.top() < 0:
                new_rect.moveTop(0)
            if new_rect.right() > widget_rect.width():
                new_rect.moveRight(widget_rect.width())
            if new_rect.bottom() > widget_rect.height():
                new_rect.moveBottom(widget_rect.height())

            self.selection_rect = new_rect
            self.update()
            self.region_changed.emit(self.selection_rect.x(), self.selection_rect.y(),
                                   self.selection_rect.width(), self.selection_rect.height())
        else:
            # Update cursor based on position
            handle = self.get_resize_handle(pos)
            if handle:
                self.setCursor(self.get_cursor_for_handle(handle))
            elif self.selection_rect.contains(pos):
                self.setCursor(Qt.CursorShape.SizeAllCursor)
            else:
                self.setCursor(Qt.CursorShape.ArrowCursor)
    
    def mouseReleaseEvent(self, event):
        """Handle mouse release events"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging = False
            self.resizing = False
            self.resize_handle = None
            self.setCursor(Qt.CursorShape.ArrowCursor)
    
    def resize_rectangle(self, pos: QPoint):
        """Resize rectangle based on handle and mouse position"""
        delta = pos - self.drag_start_pos
        new_rect = QRect(self.drag_start_rect)
        
        handle = self.resize_handle
        
        if 'left' in handle:
            new_rect.setLeft(new_rect.left() + delta.x())
        if 'right' in handle:
            new_rect.setRight(new_rect.right() + delta.x())
        if 'top' in handle:
            new_rect.setTop(new_rect.top() + delta.y())
        if 'bottom' in handle:
            new_rect.setBottom(new_rect.bottom() + delta.y())
        
        # Enforce minimum size
        if new_rect.width() < self.min_size.width():
            if 'left' in handle:
                new_rect.setLeft(new_rect.right() - self.min_size.width())
            else:
                new_rect.setRight(new_rect.left() + self.min_size.width())
        
        if new_rect.height() < self.min_size.height():
            if 'top' in handle:
                new_rect.setTop(new_rect.bottom() - self.min_size.height())
            else:
                new_rect.setBottom(new_rect.top() + self.min_size.height())
        
        # Keep within widget bounds
        widget_rect = self.rect()
        new_rect = new_rect.intersected(widget_rect)

        self.selection_rect = new_rect
        self.update()
        self.region_changed.emit(self.selection_rect.x(), self.selection_rect.y(),
                               self.selection_rect.width(), self.selection_rect.height())
    
    def paintEvent(self, event):
        """Paint the rectangle and handles"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Draw rectangle fill
        painter.fillRect(self.selection_rect, self.fill_color)

        # Draw rectangle border
        pen = QPen(self.border_color, 2)
        painter.setPen(pen)
        painter.drawRect(self.selection_rect)
        
        # Draw resize handles
        painter.setPen(QPen(self.handle_color, 1))
        painter.setBrush(QBrush(self.handle_color))
        
        handles = self.get_handle_rects()
        for handle_rect in handles.values():
            painter.drawRect(handle_rect)


class TemplatePreviewWidget(QLabel):
    """Widget for displaying template preview with OCR region overlay"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(640, 480)
        self.setStyleSheet("border: 1px solid #ccc; background-color: #f0f0f0;")
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Create resizable rectangle overlay
        self.ocr_rectangle = ResizableRectangle(self)
        self.ocr_rectangle.region_changed.connect(self.on_region_changed)
        
        # Load default template or create placeholder
        self.load_default_template()
    
    def load_default_template(self):
        """Load default template image"""
        # Create a sample livestream template
        template = self.create_sample_template()
        
        # Convert to QPixmap and display
        height, width, channel = template.shape
        bytes_per_line = 3 * width
        q_image = QImage(template.data, width, height, bytes_per_line, QImage.Format.Format_RGB888)
        pixmap = QPixmap.fromImage(q_image)
        
        # Scale to fit widget while maintaining aspect ratio
        scaled_pixmap = pixmap.scaled(self.size(), Qt.AspectRatioMode.KeepAspectRatio, 
                                    Qt.TransformationMode.SmoothTransformation)
        self.setPixmap(scaled_pixmap)
        
        # Set default OCR region (bottom third of screen)
        self.set_default_ocr_region()
    
    def create_sample_template(self) -> np.ndarray:
        """Create a sample livestream template"""
        # Create a 854x480 template (standard livestream resolution)
        template = np.ones((480, 854, 3), dtype=np.uint8) * 40  # Dark background
        
        # Add title area
        cv2.rectangle(template, (0, 0), (854, 80), (60, 60, 60), -1)
        cv2.putText(template, "My Funded Futures Live Stream", (50, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 2)
        
        # Add main content area
        cv2.rectangle(template, (50, 100), (804, 350), (80, 80, 80), -1)
        cv2.putText(template, "MAIN CONTENT AREA", (300, 225), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (200, 200, 200), 2)
        
        # Add code display area (where codes typically appear)
        cv2.rectangle(template, (100, 370), (754, 450), (0, 100, 0), 2)
        cv2.putText(template, "CODE DISPLAY AREA", (280, 400), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
        cv2.putText(template, "FREE 50 STARTER: START123", (200, 430), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        # Convert BGR to RGB for Qt
        template = cv2.cvtColor(template, cv2.COLOR_BGR2RGB)
        
        return template
    
    def set_default_ocr_region(self):
        """Set default OCR region to bottom third"""
        widget_size = self.size()

        # Default to new OCR region: 0.0, 66.9, 100, 32.9
        x = int(widget_size.width() * 0.0)    # 0% from left
        y = int(widget_size.height() * 0.669) # 66.9% from top
        width = int(widget_size.width() * 1.0)    # 100% width
        height = int(widget_size.height() * 0.329) # 32.9% height

        self.ocr_rectangle.set_rectangle(x, y, width, height)
    
    def on_region_changed(self, x: int, y: int, width: int, height: int):
        """Handle OCR region change"""
        # This signal can be connected to update other components
        pass
    
    def get_ocr_region_percent(self) -> Tuple[float, float, float, float]:
        """Get OCR region as percentages"""
        return self.ocr_rectangle.get_relative_rectangle()
    
    def set_ocr_region_percent(self, x_percent: float, y_percent: float, 
                              w_percent: float, h_percent: float):
        """Set OCR region from percentages"""
        self.ocr_rectangle.set_relative_rectangle(x_percent, y_percent, w_percent, h_percent)
    
    def resizeEvent(self, event):
        """Handle widget resize"""
        super().resizeEvent(event)
        
        # Resize the OCR rectangle overlay to match
        self.ocr_rectangle.resize(self.size())
        
        # Reload template to fit new size
        if hasattr(self, 'pixmap') and self.pixmap():
            scaled_pixmap = self.pixmap().scaled(self.size(), 
                                               Qt.AspectRatioMode.KeepAspectRatio, 
                                               Qt.TransformationMode.SmoothTransformation)
            self.setPixmap(scaled_pixmap)


class OCRRegionSelectorDialog(QDialog):
    """Dialog for manual OCR region selection"""
    
    def __init__(self, parent=None, initial_region: Tuple[float, float, float, float] = None):
        super().__init__(parent)
        self.setWindowTitle("Manual OCR Region Selection")
        self.setModal(True)
        self.resize(900, 700)
        
        self.init_ui()
        
        # Set initial region if provided
        if initial_region:
            x, y, w, h = initial_region
            self.template_preview.set_ocr_region_percent(x, y, w, h)
            self.update_coordinate_inputs(x, y, w, h)
    
    def init_ui(self):
        """Initialize dialog UI"""
        layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("🎯 Manual OCR Region Selection")
        header_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        header_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(header_label)
        
        # Instructions
        instructions = QLabel(
            "Drag the red rectangle to position the OCR scanning area.\n"
            "Resize using the corner and edge handles. The region will be used for code detection.\n"
            "The red rectangle shows your current OCR region settings."
        )
        instructions.setWordWrap(True)
        instructions.setAlignment(Qt.AlignmentFlag.AlignCenter)
        instructions.setStyleSheet("color: #666; margin: 10px;")
        layout.addWidget(instructions)

        # Current region display
        self.current_region_label = QLabel("Current Region: Loading...")
        self.current_region_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_region_label.setStyleSheet("color: #d32f2f; font-weight: bold; font-size: 14px; padding: 5px; background-color: #ffebee; border-radius: 5px;")
        layout.addWidget(self.current_region_label)
        
        # Template preview with OCR region
        preview_group = QGroupBox("📺 Livestream Template Preview")
        preview_layout = QVBoxLayout(preview_group)
        
        self.template_preview = TemplatePreviewWidget()
        self.template_preview.ocr_rectangle.region_changed.connect(self.on_region_changed)
        preview_layout.addWidget(self.template_preview)
        
        layout.addWidget(preview_group)
        
        # Coordinate controls
        coords_group = QGroupBox("📐 Precise Coordinates")
        coords_layout = QGridLayout(coords_group)
        
        # X Position
        coords_layout.addWidget(QLabel("X Position (%):"), 0, 0)
        self.x_spin = QDoubleSpinBox()
        self.x_spin.setRange(0.0, 100.0)
        self.x_spin.setSuffix("%")
        self.x_spin.valueChanged.connect(self.on_coordinate_changed)
        coords_layout.addWidget(self.x_spin, 0, 1)
        
        # Y Position
        coords_layout.addWidget(QLabel("Y Position (%):"), 0, 2)
        self.y_spin = QDoubleSpinBox()
        self.y_spin.setRange(0.0, 100.0)
        self.y_spin.setSuffix("%")
        self.y_spin.valueChanged.connect(self.on_coordinate_changed)
        coords_layout.addWidget(self.y_spin, 0, 3)
        
        # Width
        coords_layout.addWidget(QLabel("Width (%):"), 1, 0)
        self.w_spin = QDoubleSpinBox()
        self.w_spin.setRange(1.0, 100.0)
        self.w_spin.setSuffix("%")
        self.w_spin.valueChanged.connect(self.on_coordinate_changed)
        coords_layout.addWidget(self.w_spin, 1, 1)
        
        # Height
        coords_layout.addWidget(QLabel("Height (%):"), 1, 2)
        self.h_spin = QDoubleSpinBox()
        self.h_spin.setRange(1.0, 100.0)
        self.h_spin.setSuffix("%")
        self.h_spin.valueChanged.connect(self.on_coordinate_changed)
        coords_layout.addWidget(self.h_spin, 1, 3)
        
        layout.addWidget(coords_group)
        
        # Preset buttons
        presets_group = QGroupBox("🎛️ Quick Presets")
        presets_layout = QHBoxLayout(presets_group)
        
        bottom_third_btn = QPushButton("Bottom Third")
        bottom_third_btn.clicked.connect(lambda: self.apply_preset(0, 66.9, 100, 32.9))
        presets_layout.addWidget(bottom_third_btn)
        
        bottom_quarter_btn = QPushButton("Bottom Quarter")
        bottom_quarter_btn.clicked.connect(lambda: self.apply_preset(0, 75, 100, 25))
        presets_layout.addWidget(bottom_quarter_btn)
        
        center_btn = QPushButton("Center")
        center_btn.clicked.connect(lambda: self.apply_preset(25, 25, 50, 50))
        presets_layout.addWidget(center_btn)
        
        full_screen_btn = QPushButton("Full Screen")
        full_screen_btn.clicked.connect(lambda: self.apply_preset(0, 0, 100, 100))
        presets_layout.addWidget(full_screen_btn)
        
        layout.addWidget(presets_group)
        
        # Dialog buttons
        button_layout = QHBoxLayout()
        
        ok_btn = QPushButton("✅ Apply Region")
        ok_btn.clicked.connect(self.accept)
        ok_btn.setStyleSheet("font-weight: bold; background-color: #4CAF50; color: white; padding: 8px;")
        button_layout.addWidget(ok_btn)
        
        cancel_btn = QPushButton("❌ Cancel")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
    
    def on_region_changed(self, x: int, y: int, width: int, height: int):
        """Handle region change from rectangle"""
        x_percent, y_percent, w_percent, h_percent = self.template_preview.get_ocr_region_percent()
        self.update_coordinate_inputs(x_percent, y_percent, w_percent, h_percent)
    
    def on_coordinate_changed(self):
        """Handle coordinate change from spin boxes"""
        x = self.x_spin.value()
        y = self.y_spin.value()
        w = self.w_spin.value()
        h = self.h_spin.value()
        
        self.template_preview.set_ocr_region_percent(x, y, w, h)
    
    def update_coordinate_inputs(self, x: float, y: float, w: float, h: float):
        """Update coordinate input fields"""
        self.x_spin.blockSignals(True)
        self.y_spin.blockSignals(True)
        self.w_spin.blockSignals(True)
        self.h_spin.blockSignals(True)

        self.x_spin.setValue(x)
        self.y_spin.setValue(y)
        self.w_spin.setValue(w)
        self.h_spin.setValue(h)

        self.x_spin.blockSignals(False)
        self.y_spin.blockSignals(False)
        self.w_spin.blockSignals(False)
        self.h_spin.blockSignals(False)

        # Update current region display
        if hasattr(self, 'current_region_label'):
            self.current_region_label.setText(f"Current Region: X:{x:.1f}%, Y:{y:.1f}%, W:{w:.1f}%, H:{h:.1f}%")
    
    def apply_preset(self, x: float, y: float, w: float, h: float):
        """Apply a preset region"""
        self.template_preview.set_ocr_region_percent(x, y, w, h)
        self.update_coordinate_inputs(x, y, w, h)
    
    def get_selected_region(self) -> Tuple[float, float, float, float]:
        """Get the selected OCR region as percentages"""
        return self.template_preview.get_ocr_region_percent()


def test_ocr_region_selector():
    """Test the OCR region selector"""
    app = QApplication(sys.argv)
    
    # Test with initial region
    initial_region = (0.0, 66.9, 100.0, 32.9)  # Bottom third
    
    dialog = OCRRegionSelectorDialog(initial_region=initial_region)
    
    if dialog.exec() == QDialog.DialogCode.Accepted:
        selected_region = dialog.get_selected_region()
        print(f"Selected OCR Region: {selected_region}")
        print(f"X: {selected_region[0]:.1f}%, Y: {selected_region[1]:.1f}%")
        print(f"Width: {selected_region[2]:.1f}%, Height: {selected_region[3]:.1f}%")
    else:
        print("Region selection cancelled")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    test_ocr_region_selector()
