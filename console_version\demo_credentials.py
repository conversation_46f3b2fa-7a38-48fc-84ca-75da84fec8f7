#!/usr/bin/env python3
"""
Demo script showing the credentials management system
"""

import os
import sys

# Add support folder to path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'MFFUHijack_Support'))

def demo_credentials_flow():
    """Demo the credentials management flow"""
    from mffuhijack_console import ConsoleInterface, CredentialsManager
    
    console = ConsoleInterface()
    credentials = CredentialsManager()
    
    print("=" * 60)
    print("🔐 CREDENTIALS MANAGEMENT DEMO")
    print("=" * 60)
    
    # Clear any existing credentials for demo
    credentials.clear_credentials()
    
    print("\n1. First time - no saved credentials:")
    print("   📝 No saved credentials found.")
    print("   Username: testuser")
    print("   Password: ********")
    print("   Save credentials for future use? (y/n): y")
    print("   ✅ Credentials saved.")
    
    # Simulate saving credentials
    credentials.set_credentials("testuser", "testpass123")
    
    print("\n2. Second time - credentials found:")
    print("   ✅ Found saved credentials for: testuser")
    print("     [1] Use saved credentials")
    print("     [2] Enter new credentials")
    print("     [3] Clear saved credentials")
    print("   Select option (1-3): 1")
    print("   ✅ Using saved credentials: testuser")
    
    print("\n3. If user wants to change credentials:")
    print("   ✅ Found saved credentials for: testuser")
    print("     [1] Use saved credentials")
    print("     [2] Enter new credentials")
    print("     [3] Clear saved credentials")
    print("   Select option (1-3): 2")
    print("   Username: newuser")
    print("   Password: ********")
    print("   Save credentials for future use? (y/n): y")
    print("   ✅ Credentials saved.")
    
    print("\n4. If user wants to clear credentials:")
    print("   ✅ Found saved credentials for: newuser")
    print("     [1] Use saved credentials")
    print("     [2] Enter new credentials")
    print("     [3] Clear saved credentials")
    print("   Select option (1-3): 3")
    print("   Saved credentials cleared.")
    print("   📝 No saved credentials found.")
    print("   Username: anotheruser")
    print("   Password: ********")
    print("   Save credentials for future use? (y/n): n")
    
    print("\n" + "=" * 60)
    print("✅ CREDENTIALS DEMO COMPLETE")
    print("=" * 60)
    
    print("\n🎯 How it works:")
    print("• First time: User enters credentials, option to save")
    print("• Subsequent times: Option to use saved, enter new, or clear")
    print("• Credentials stored in: user_credentials.json")
    print("• Password is stored (consider encryption for production)")
    print("• No more repetitive credential entry!")
    
    # Clean up demo credentials
    credentials.clear_credentials()
    print("\n🧹 Demo credentials cleared.")

def test_credentials_manager():
    """Test the credentials manager functionality"""
    from mffuhijack_console import CredentialsManager
    
    print("\n" + "=" * 60)
    print("🧪 TESTING CREDENTIALS MANAGER")
    print("=" * 60)
    
    creds = CredentialsManager()
    
    # Test 1: No credentials initially
    print("\n1. Testing initial state:")
    has_creds = creds.has_credentials()
    print(f"   Has credentials: {has_creds}")
    assert not has_creds, "Should have no credentials initially"
    print("   ✅ PASS: No initial credentials")
    
    # Test 2: Set credentials
    print("\n2. Testing set credentials:")
    creds.set_credentials("testuser", "testpass")
    has_creds = creds.has_credentials()
    username, password = creds.get_credentials()
    print(f"   Has credentials: {has_creds}")
    print(f"   Username: {username}")
    print(f"   Password: {'*' * len(password)}")
    assert has_creds, "Should have credentials after setting"
    assert username == "testuser", "Username should match"
    assert password == "testpass", "Password should match"
    print("   ✅ PASS: Credentials set and retrieved correctly")
    
    # Test 3: Clear credentials
    print("\n3. Testing clear credentials:")
    creds.clear_credentials()
    has_creds = creds.has_credentials()
    print(f"   Has credentials: {has_creds}")
    assert not has_creds, "Should have no credentials after clearing"
    print("   ✅ PASS: Credentials cleared successfully")
    
    # Test 4: Persistence (create new instance)
    print("\n4. Testing persistence:")
    creds.set_credentials("persistent_user", "persistent_pass")
    
    # Create new instance to test file persistence
    new_creds = CredentialsManager()
    has_creds = new_creds.has_credentials()
    username, password = new_creds.get_credentials()
    print(f"   New instance has credentials: {has_creds}")
    print(f"   Username: {username}")
    assert has_creds, "New instance should load saved credentials"
    assert username == "persistent_user", "Username should persist"
    print("   ✅ PASS: Credentials persist across instances")
    
    # Clean up
    new_creds.clear_credentials()
    
    print("\n🏁 All credentials manager tests passed!")

def main():
    """Main demo function"""
    try:
        demo_credentials_flow()
        test_credentials_manager()
        
        print("\n" + "=" * 60)
        print("🎉 CREDENTIALS SYSTEM READY!")
        print("=" * 60)
        print("\n✨ Features:")
        print("• Automatic credential saving and loading")
        print("• User choice to use saved or enter new")
        print("• Option to clear saved credentials")
        print("• No more repetitive username/password entry")
        print("• Seamless integration with both scanner modes")
        
        print("\n🚀 Try it out:")
        print("• Run: python mffuhijack_console.py")
        print("• Enter credentials once and save them")
        print("• Next time, just select option 1 to use saved credentials")
        
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
