"""
Live Scan Monitor Window for MFFU<PERSON>ijack
Dedicated window for real-time scanning session monitoring and statistics
"""

import sys
import time
import json
import csv
import os
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional

# Try to import psutil, use replacement if not available
try:
    import psutil
except ImportError:
    try:
        import psutil_replacement as psutil
        print("Using psutil replacement for system monitoring")
    except ImportError:
        print("Warning: No system monitoring available")
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QTextEdit, QGroupBox, QFrame, QScrollArea, QProgressBar,
    QPushButton, QSplitter, QTabWidget, QCheckBox, QSlider, QComboBox,
    QMenuBar, QMenu, QFileDialog, QMessageBox, QSystemTrayIcon,
    QApplication, QSpinBox, QDoubleSpinBox, QColorDialog, QFontDialog,
    QDialog, QDialogButtonBox, QTreeWidget, QTreeWidgetItem, QHeaderView,
    QRubberBand, QToolButton, QButtonGroup, QRadioButton
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QObject, QSettings, QRect, QPoint, QSize, QThread
from PyQt6.QtGui import (
    QPixmap, QFont, QPalette, QColor, QPainter, QPen, QIcon, QAction,
    QMouseEvent, QPaintEvent, QResizeEvent, QKeyEvent, QCursor, QBrush
)

# Import smart features
try:
    from smart_features import SmartFeaturesManager, TimeBasedAnalyzer, CodeHistoryManager, SmartRetrySystem
    from temporal_analytics_dashboard import TemporalAnalyticsDashboard
    SMART_FEATURES_AVAILABLE = True
except ImportError as e:
    print(f"Smart features not available: {e}")
    SMART_FEATURES_AVAILABLE = False

# Import testing mode
try:
    from livestream_testing_gui import LiveStreamTestingWindow
    TESTING_MODE_AVAILABLE = True
except ImportError as e:
    print(f"Testing mode not available: {e}")
    TESTING_MODE_AVAILABLE = False

# Import manual OCR region selector
try:
    from manual_ocr_region_selector import OCRRegionSelectorDialog
    OCR_REGION_SELECTOR_AVAILABLE = True
except ImportError as e:
    print(f"OCR region selector not available: {e}")
    OCR_REGION_SELECTOR_AVAILABLE = False


class ThemeManager:
    """Manages themes and styling for the Live Scan Monitor"""

    LIGHT_THEME = {
        'background': '#f5f5f5',
        'surface': '#ffffff',
        'primary': '#4CAF50',
        'secondary': '#2196F3',
        'text_primary': '#212121',
        'text_secondary': '#757575',
        'success': '#4CAF50',
        'warning': '#FF9800',
        'error': '#F44336',
        'border': '#e0e0e0',
        'accent': '#9C27B0'
    }

    DARK_THEME = {
        'background': '#121212',
        'surface': '#1e1e1e',
        'primary': '#66BB6A',
        'secondary': '#42A5F5',
        'text_primary': '#ffffff',
        'text_secondary': '#b0b0b0',
        'success': '#66BB6A',
        'warning': '#FFB74D',
        'error': '#EF5350',
        'border': '#333333',
        'accent': '#BA68C8'
    }

    def __init__(self):
        self.current_theme = 'light'
        self.themes = {
            'light': self.LIGHT_THEME,
            'dark': self.DARK_THEME
        }

    def get_theme(self, theme_name: str = None) -> Dict[str, str]:
        """Get theme colors"""
        if theme_name is None:
            theme_name = self.current_theme
        return self.themes.get(theme_name, self.LIGHT_THEME)

    def set_theme(self, theme_name: str):
        """Set current theme"""
        if theme_name in self.themes:
            self.current_theme = theme_name

    def get_stylesheet(self, theme_name: str = None) -> str:
        """Generate stylesheet for current theme"""
        theme = self.get_theme(theme_name)

        return f"""
            QMainWindow {{
                background-color: {theme['background']};
                color: {theme['text_primary']};
            }}
            QFrame {{
                background-color: {theme['surface']};
                border: 1px solid {theme['border']};
                border-radius: 8px;
                padding: 8px;
            }}
            QGroupBox {{
                font-weight: bold;
                border: 2px solid {theme['border']};
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 12px;
                color: {theme['text_primary']};
                background-color: {theme['surface']};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                color: {theme['primary']};
                font-weight: bold;
            }}
            QPushButton {{
                background-color: {theme['primary']};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: {theme['secondary']};
            }}
            QPushButton:pressed {{
                background-color: {theme['accent']};
            }}
            QPushButton:disabled {{
                background-color: {theme['border']};
                color: {theme['text_secondary']};
            }}
            QLabel {{
                color: {theme['text_primary']};
                background-color: transparent;
            }}
            QTextEdit {{
                background-color: {theme['surface']};
                border: 1px solid {theme['border']};
                border-radius: 4px;
                color: {theme['text_primary']};
                font-family: 'Courier New', monospace;
            }}
            QSlider::groove:horizontal {{
                border: 1px solid {theme['border']};
                height: 6px;
                background: {theme['background']};
                border-radius: 3px;
            }}
            QSlider::handle:horizontal {{
                background: {theme['primary']};
                border: 1px solid {theme['border']};
                width: 16px;
                margin: -5px 0;
                border-radius: 8px;
            }}
            QComboBox {{
                background-color: {theme['surface']};
                border: 1px solid {theme['border']};
                border-radius: 4px;
                padding: 6px;
                color: {theme['text_primary']};
            }}
            QCheckBox {{
                color: {theme['text_primary']};
            }}
            QCheckBox::indicator:checked {{
                background-color: {theme['primary']};
                border: 1px solid {theme['primary']};
            }}
        """


class PerformanceMonitor:
    """Monitors system and application performance"""

    def __init__(self):
        self.reset()

    def reset(self):
        """Reset performance counters"""
        self.frame_count = 0
        self.processing_times = []
        self.memory_usage = []
        self.cpu_usage = []
        self.start_time = time.time()
        self.last_frame_time = time.time()

    def record_frame_processing(self, processing_time: float):
        """Record frame processing time"""
        self.frame_count += 1
        self.processing_times.append(processing_time)
        self.last_frame_time = time.time()

        # Keep only last 100 measurements
        if len(self.processing_times) > 100:
            self.processing_times = self.processing_times[-100:]

    def record_system_stats(self):
        """Record system performance stats"""
        try:
            # Memory usage
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            self.memory_usage.append(memory_mb)

            # CPU usage
            cpu_percent = psutil.cpu_percent()
            self.cpu_usage.append(cpu_percent)

            # Keep only last 60 measurements (1 minute at 1 second intervals)
            if len(self.memory_usage) > 60:
                self.memory_usage = self.memory_usage[-60:]
            if len(self.cpu_usage) > 60:
                self.cpu_usage = self.cpu_usage[-60:]

        except Exception as e:
            print(f"Error recording system stats: {e}")

    def get_fps(self) -> float:
        """Calculate current FPS"""
        if len(self.processing_times) < 2:
            return 0.0

        # Calculate FPS based on recent processing times
        recent_times = self.processing_times[-10:]  # Last 10 frames
        if not recent_times:
            return 0.0

        avg_time = sum(recent_times) / len(recent_times)
        return 1.0 / avg_time if avg_time > 0 else 0.0

    def get_avg_processing_time(self) -> float:
        """Get average processing time in milliseconds"""
        if not self.processing_times:
            return 0.0
        return (sum(self.processing_times) / len(self.processing_times)) * 1000

    def get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        return self.memory_usage[-1] if self.memory_usage else 0.0

    def get_cpu_usage(self) -> float:
        """Get current CPU usage percentage"""
        return self.cpu_usage[-1] if self.cpu_usage else 0.0


class SessionHistory:
    """Manages session history and export functionality"""

    def __init__(self):
        self.sessions = []
        self.current_session = None
        self.history_file = "scan_session_history.json"
        self.load_history()

    def start_session(self, stream_url: str) -> str:
        """Start a new session"""
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.current_session = {
            'id': session_id,
            'stream_url': stream_url,
            'start_time': datetime.now().isoformat(),
            'end_time': None,
            'total_codes': 0,
            'valid_codes': 0,
            'invalid_codes': 0,
            'codes_by_type': {},
            'detection_events': [],
            'performance_stats': {
                'avg_fps': 0.0,
                'avg_processing_time': 0.0,
                'peak_memory': 0.0,
                'avg_cpu': 0.0
            }
        }
        return session_id

    def end_session(self, statistics, performance_monitor):
        """End current session and save to history"""
        if self.current_session:
            self.current_session['end_time'] = datetime.now().isoformat()
            self.current_session['total_codes'] = statistics.total_codes_detected
            self.current_session['valid_codes'] = statistics.valid_codes_found
            self.current_session['invalid_codes'] = statistics.invalid_codes_found
            self.current_session['codes_by_type'] = statistics.codes_by_type.copy()
            self.current_session['detection_events'] = [
                {
                    'timestamp': event['timestamp'].isoformat(),
                    'code': event['code'],
                    'type': event['type'],
                    'valid': event['valid'],
                    'confidence': event['confidence']
                }
                for event in statistics.detection_events
            ]

            # Performance stats
            self.current_session['performance_stats'] = {
                'avg_fps': performance_monitor.get_fps(),
                'avg_processing_time': performance_monitor.get_avg_processing_time(),
                'peak_memory': max(performance_monitor.memory_usage) if performance_monitor.memory_usage else 0.0,
                'avg_cpu': sum(performance_monitor.cpu_usage) / len(performance_monitor.cpu_usage) if performance_monitor.cpu_usage else 0.0
            }

            self.sessions.append(self.current_session)
            self.current_session = None
            self.save_history()

    def load_history(self):
        """Load session history from file"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r') as f:
                    self.sessions = json.load(f)
        except Exception as e:
            print(f"Error loading session history: {e}")
            self.sessions = []

    def save_history(self):
        """Save session history to file"""
        try:
            with open(self.history_file, 'w') as f:
                json.dump(self.sessions, f, indent=2)
        except Exception as e:
            print(f"Error saving session history: {e}")

    def export_to_csv(self, filename: str):
        """Export session history to CSV"""
        try:
            with open(filename, 'w', newline='') as csvfile:
                fieldnames = [
                    'session_id', 'stream_url', 'start_time', 'end_time',
                    'total_codes', 'valid_codes', 'invalid_codes', 'success_rate',
                    'avg_fps', 'avg_processing_time', 'peak_memory', 'avg_cpu'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for session in self.sessions:
                    success_rate = (session['valid_codes'] / session['total_codes'] * 100) if session['total_codes'] > 0 else 0
                    writer.writerow({
                        'session_id': session['id'],
                        'stream_url': session['stream_url'],
                        'start_time': session['start_time'],
                        'end_time': session['end_time'],
                        'total_codes': session['total_codes'],
                        'valid_codes': session['valid_codes'],
                        'invalid_codes': session['invalid_codes'],
                        'success_rate': f"{success_rate:.1f}%",
                        'avg_fps': session['performance_stats']['avg_fps'],
                        'avg_processing_time': session['performance_stats']['avg_processing_time'],
                        'peak_memory': session['performance_stats']['peak_memory'],
                        'avg_cpu': session['performance_stats']['avg_cpu']
                    })
            return True
        except Exception as e:
            print(f"Error exporting to CSV: {e}")
            return False

    def export_to_json(self, filename: str):
        """Export session history to JSON"""
        try:
            with open(filename, 'w') as f:
                json.dump(self.sessions, f, indent=2)
            return True
        except Exception as e:
            print(f"Error exporting to JSON: {e}")
            return False


class NotificationManager:
    """Manages desktop notifications for the Live Scan Monitor"""

    def __init__(self):
        self.tray_icon = None
        self.notifications_enabled = True
        self.sound_enabled = True
        self.setup_system_tray()

    def setup_system_tray(self):
        """Setup system tray icon for notifications"""
        try:
            if QSystemTrayIcon.isSystemTrayAvailable():
                self.tray_icon = QSystemTrayIcon()
                # Create a simple icon (you can replace with actual icon file)
                icon = QIcon()
                self.tray_icon.setIcon(icon)
                self.tray_icon.setToolTip("MFFUHijack Live Scan Monitor")
                self.tray_icon.show()
        except Exception as e:
            print(f"Error setting up system tray: {e}")

    def show_notification(self, title: str, message: str, notification_type: str = "info"):
        """Show desktop notification"""
        if not self.notifications_enabled:
            return

        try:
            if self.tray_icon and QSystemTrayIcon.isSystemTrayAvailable():
                # Map notification types to system tray icon types
                icon_type = QSystemTrayIcon.MessageIcon.Information
                if notification_type == "warning":
                    icon_type = QSystemTrayIcon.MessageIcon.Warning
                elif notification_type == "error":
                    icon_type = QSystemTrayIcon.MessageIcon.Critical
                elif notification_type == "success":
                    icon_type = QSystemTrayIcon.MessageIcon.Information

                self.tray_icon.showMessage(title, message, icon_type, 3000)  # 3 second duration
            else:
                # Fallback to console output if system tray not available
                print(f"NOTIFICATION [{notification_type.upper()}]: {title} - {message}")
        except Exception as e:
            print(f"Error showing notification: {e}")

    def notify_code_detected(self, code_data: Dict):
        """Send notification for code detection"""
        code = code_data.get('code', '')
        code_type = code_data.get('type', 'Unknown')
        confidence = code_data.get('confidence', 0)

        if self.is_valid_code(code_data):
            title = f"✅ Valid Code Detected!"
            message = f"{code_type}: {code}\nConfidence: {confidence:.1%}"
            self.show_notification(title, message, "success")
        else:
            title = f"❌ Invalid Code Detected"
            message = f"{code_type}: {code}\nConfidence: {confidence:.1%}"
            self.show_notification(title, message, "warning")

    def notify_session_event(self, event_type: str, message: str):
        """Send notification for session events"""
        titles = {
            "start": "🚀 Scanning Started",
            "stop": "⏹️ Scanning Stopped",
            "error": "❌ Scanning Error",
            "milestone": "🎯 Milestone Reached"
        }

        title = titles.get(event_type, "📢 Scan Update")
        notification_type = "error" if event_type == "error" else "info"
        self.show_notification(title, message, notification_type)

    def is_valid_code(self, code_data: Dict) -> bool:
        """Check if detected code is valid (same logic as ScanStatistics)"""
        code = code_data.get('code', '')
        code_type = code_data.get('type', '')
        confidence = code_data.get('confidence', 0)

        if len(code) < 3:
            return False
        if confidence < 0.5:
            return False
        if code_type not in ["Reset", "Starter", "Starter Plus", "Expert", "Free Reset Code"]:
            return False

        return True

    def set_notifications_enabled(self, enabled: bool):
        """Enable or disable notifications"""
        self.notifications_enabled = enabled

    def set_sound_enabled(self, enabled: bool):
        """Enable or disable notification sounds"""
        self.sound_enabled = enabled


class ScanStatistics:
    """Enhanced class to track scanning session statistics"""

    def __init__(self):
        self.reset()

    def reset(self):
        """Reset all statistics"""
        self.session_start_time = datetime.now()
        self.total_codes_detected = 0
        self.valid_codes_found = 0
        self.invalid_codes_found = 0
        self.codes_by_type = {
            "Reset": 0,
            "Starter": 0,
            "Starter Plus": 0,
            "Expert": 0,
            "Free Reset Code": 0
        }
        self.detection_events = []
        self.last_frame_timestamp = None
        self.frames_processed = 0
        self.screenshots_taken = 0
        self.session_milestones = []
    
    def add_code_detection(self, code_data: Dict):
        """Add a new code detection event"""
        event = {
            'timestamp': datetime.now(),
            'code': code_data.get('code', ''),
            'type': code_data.get('type', 'Unknown'),
            'full_text': code_data.get('full_text', ''),
            'confidence': code_data.get('confidence', 0),
            'valid': self.is_valid_code(code_data)
        }
        
        self.detection_events.append(event)
        self.total_codes_detected += 1
        
        if event['valid']:
            self.valid_codes_found += 1
            if event['type'] in self.codes_by_type:
                self.codes_by_type[event['type']] += 1
        else:
            self.invalid_codes_found += 1
    
    def is_valid_code(self, code_data: Dict) -> bool:
        """Determine if a detected code is valid"""
        code = code_data.get('code', '')
        code_type = code_data.get('type', '')
        confidence = code_data.get('confidence', 0)
        
        # Basic validation criteria
        if len(code) < 3:
            return False
        if confidence < 0.5:
            return False
        if code_type not in self.codes_by_type:
            return False
        
        return True
    
    def get_success_rate(self) -> float:
        """Calculate success rate percentage"""
        if self.total_codes_detected == 0:
            return 0.0
        return (self.valid_codes_found / self.total_codes_detected) * 100
    
    def get_session_duration(self) -> timedelta:
        """Get current session duration"""
        return datetime.now() - self.session_start_time
    
    def update_frame_info(self, frame_number: int = None, timestamp: str = None):
        """Update frame processing information"""
        self.frames_processed += 1
        if timestamp:
            self.last_frame_timestamp = timestamp

    def add_milestone(self, milestone_type: str, description: str):
        """Add a session milestone"""
        milestone = {
            'timestamp': datetime.now(),
            'type': milestone_type,
            'description': description
        }
        self.session_milestones.append(milestone)


class InteractivePreviewWidget(QLabel):
    """Interactive preview widget with region adjustment and zoom capabilities"""

    # Signals
    region_changed = pyqtSignal(tuple)  # (x, y, width, height) as percentages
    screenshot_requested = pyqtSignal()
    zoom_requested = pyqtSignal(QPoint)  # Point to zoom to

    def __init__(self):
        super().__init__()
        self.setMinimumSize(400, 300)
        self.setStyleSheet("""
            QLabel {
                border: 2px solid #ccc;
                background-color: #000;
                color: white;
            }
        """)
        self.setText("No video feed")
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Region adjustment properties
        self.ocr_region = (0, 67, 100, 33)  # x, y, width, height as percentages
        self.is_adjusting_region = False
        self.region_start_point = None
        self.region_rubber_band = None
        self.current_pixmap = None
        self.zoom_factor = 1.0
        self.zoom_offset = QPoint(0, 0)
        self.is_dragging = False
        self.last_pan_point = None

        # Enable mouse tracking
        self.setMouseTracking(True)
        self.setCursor(QCursor(Qt.CursorShape.CrossCursor))

    def set_ocr_region(self, region: tuple):
        """Set the OCR region (x, y, width, height as percentages)"""
        self.ocr_region = region
        self.update()

    def get_ocr_region(self) -> tuple:
        """Get the current OCR region"""
        return self.ocr_region

    def update_frame(self, pixmap: QPixmap):
        """Update the frame with OCR region overlay"""
        if pixmap and not pixmap.isNull():
            self.current_pixmap = pixmap.copy()

            # Apply zoom if needed
            if self.zoom_factor != 1.0:
                scaled_size = pixmap.size() * self.zoom_factor
                pixmap = pixmap.scaled(scaled_size, Qt.AspectRatioMode.KeepAspectRatio,
                                     Qt.TransformationMode.SmoothTransformation)

            # Scale pixmap to fit widget
            scaled_pixmap = pixmap.scaled(
                self.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )

            # Draw OCR region overlay
            self.draw_ocr_overlay(scaled_pixmap)
            self.setPixmap(scaled_pixmap)

    def draw_ocr_overlay(self, pixmap: QPixmap):
        """Draw OCR region overlay on pixmap"""
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Calculate OCR region coordinates
        width = pixmap.width()
        height = pixmap.height()

        x = int(width * self.ocr_region[0] / 100)
        y = int(height * self.ocr_region[1] / 100)
        w = int(width * self.ocr_region[2] / 100)
        h = int(height * self.ocr_region[3] / 100)

        # Draw OCR region rectangle
        pen = QPen(QColor(255, 0, 0), 3)  # Red, 3px thick
        painter.setPen(pen)
        painter.drawRect(x, y, w, h)

        # Draw corner handles for resizing
        handle_size = 8
        brush = QBrush(QColor(255, 0, 0))
        painter.setBrush(brush)

        # Corner handles
        painter.drawRect(x - handle_size//2, y - handle_size//2, handle_size, handle_size)
        painter.drawRect(x + w - handle_size//2, y - handle_size//2, handle_size, handle_size)
        painter.drawRect(x - handle_size//2, y + h - handle_size//2, handle_size, handle_size)
        painter.drawRect(x + w - handle_size//2, y + h - handle_size//2, handle_size, handle_size)

        # Draw region info
        painter.setPen(QPen(QColor(255, 255, 255), 1))
        info_text = f"OCR Region: {self.ocr_region[0]:.1f}%, {self.ocr_region[1]:.1f}%, {self.ocr_region[2]:.1f}% × {self.ocr_region[3]:.1f}%"
        painter.drawText(x, y - 5, info_text)

        painter.end()

    def mousePressEvent(self, event: QMouseEvent):
        """Handle mouse press for region adjustment"""
        if event.button() == Qt.MouseButton.LeftButton:
            if event.modifiers() & Qt.KeyboardModifier.ControlModifier:
                # Ctrl+Click for region adjustment
                self.start_region_adjustment(event.pos())
            elif event.modifiers() & Qt.KeyboardModifier.ShiftModifier:
                # Shift+Click for zoom
                self.zoom_requested.emit(event.pos())
            else:
                # Regular click for panning in zoom mode
                if self.zoom_factor > 1.0:
                    self.is_dragging = True
                    self.last_pan_point = event.pos()
                    self.setCursor(QCursor(Qt.CursorShape.ClosedHandCursor))
        elif event.button() == Qt.MouseButton.RightButton:
            # Right click for screenshot
            self.screenshot_requested.emit()

    def mouseMoveEvent(self, event: QMouseEvent):
        """Handle mouse move for region adjustment and panning"""
        if self.is_adjusting_region and self.region_rubber_band:
            # Update rubber band for region selection
            self.region_rubber_band.setGeometry(QRect(self.region_start_point, event.pos()).normalized())
        elif self.is_dragging and self.zoom_factor > 1.0:
            # Handle panning in zoom mode
            if self.last_pan_point:
                delta = event.pos() - self.last_pan_point
                self.zoom_offset += delta
                self.last_pan_point = event.pos()
                self.update()

    def mouseReleaseEvent(self, event: QMouseEvent):
        """Handle mouse release"""
        if event.button() == Qt.MouseButton.LeftButton:
            if self.is_adjusting_region:
                self.finish_region_adjustment(event.pos())
            elif self.is_dragging:
                self.is_dragging = False
                self.setCursor(QCursor(Qt.CursorShape.CrossCursor))

    def start_region_adjustment(self, start_point: QPoint):
        """Start region adjustment mode"""
        self.is_adjusting_region = True
        self.region_start_point = start_point

        # Create rubber band for visual feedback
        self.region_rubber_band = QRubberBand(QRubberBand.Shape.Rectangle, self)
        self.region_rubber_band.setGeometry(QRect(start_point, QSize()))
        self.region_rubber_band.show()

        self.setCursor(QCursor(Qt.CursorShape.CrossCursor))

    def finish_region_adjustment(self, end_point: QPoint):
        """Finish region adjustment and update OCR region"""
        if self.region_rubber_band:
            # Calculate new region as percentages
            rect = QRect(self.region_start_point, end_point).normalized()
            widget_size = self.size()

            if widget_size.width() > 0 and widget_size.height() > 0:
                x_percent = (rect.x() / widget_size.width()) * 100
                y_percent = (rect.y() / widget_size.height()) * 100
                width_percent = (rect.width() / widget_size.width()) * 100
                height_percent = (rect.height() / widget_size.height()) * 100

                # Ensure reasonable bounds
                x_percent = max(0, min(100, x_percent))
                y_percent = max(0, min(100, y_percent))
                width_percent = max(5, min(100 - x_percent, width_percent))
                height_percent = max(5, min(100 - y_percent, height_percent))

                new_region = (x_percent, y_percent, width_percent, height_percent)
                self.ocr_region = new_region
                self.region_changed.emit(new_region)

            # Clean up
            self.region_rubber_band.hide()
            self.region_rubber_band.deleteLater()
            self.region_rubber_band = None

        self.is_adjusting_region = False
        self.setCursor(QCursor(Qt.CursorShape.CrossCursor))
        self.update()

    def wheelEvent(self, event):
        """Handle mouse wheel for zooming"""
        if event.modifiers() & Qt.KeyboardModifier.ControlModifier:
            # Zoom with Ctrl+Wheel
            zoom_in = event.angleDelta().y() > 0
            zoom_factor = 1.1 if zoom_in else 0.9

            self.zoom_factor *= zoom_factor
            self.zoom_factor = max(0.5, min(5.0, self.zoom_factor))  # Limit zoom range

            if self.current_pixmap:
                self.update_frame(self.current_pixmap)
        else:
            super().wheelEvent(event)

    def reset_zoom(self):
        """Reset zoom to normal"""
        self.zoom_factor = 1.0
        self.zoom_offset = QPoint(0, 0)
        if self.current_pixmap:
            self.update_frame(self.current_pixmap)

    def keyPressEvent(self, event: QKeyEvent):
        """Handle keyboard shortcuts"""
        if event.key() == Qt.Key.Key_R and event.modifiers() & Qt.KeyboardModifier.ControlModifier:
            # Ctrl+R to reset zoom
            self.reset_zoom()
        elif event.key() == Qt.Key.Key_S and event.modifiers() & Qt.KeyboardModifier.ControlModifier:
            # Ctrl+S for screenshot
            self.screenshot_requested.emit()
        else:
            super().keyPressEvent(event)


class LiveScanMonitorWindow(QMainWindow):
    """Enhanced dedicated window for monitoring live scan sessions"""

    # Signals
    window_closed = pyqtSignal()
    region_changed = pyqtSignal(tuple)

    def __init__(self):
        super().__init__()

        # Core components
        self.statistics = ScanStatistics()
        self.performance_monitor = PerformanceMonitor()
        self.session_history = SessionHistory()
        self.notification_manager = NotificationManager()
        self.theme_manager = ThemeManager()

        # Smart features
        if SMART_FEATURES_AVAILABLE:
            self.smart_features = SmartFeaturesManager()
            self.temporal_dashboard = None
        else:
            self.smart_features = None
            self.temporal_dashboard = None

        # Testing mode
        self.testing_window = None

        # OCR region settings
        self.current_ocr_region = self.load_ocr_region_settings()

        # State variables
        self.is_scanning = False
        self.is_paused = False
        self.stream_url = ""
        self.stream_status = "Offline"
        self.playback_speed = 1.5
        self.current_session_id = None

        # Settings
        self.settings = QSettings("MFFUHijack", "LiveScanMonitor")
        self.load_settings()

        # UI update timers
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(1000)  # Update every second

        self.performance_timer = QTimer()
        self.performance_timer.timeout.connect(self.update_performance_stats)
        self.performance_timer.start(1000)  # Update performance every second

        # Screenshot functionality
        self.screenshots_dir = "screenshots"
        self.ensure_screenshots_dir()

        self.init_ui()
        self.apply_current_theme()
        self.setup_shortcuts()

    def load_settings(self):
        """Load user settings"""
        self.theme_manager.set_theme(self.settings.value("theme", "light"))
        self.notification_manager.set_notifications_enabled(
            self.settings.value("notifications_enabled", True, type=bool)
        )

        # Load window geometry
        geometry = self.settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)

    def save_settings(self):
        """Save user settings"""
        self.settings.setValue("theme", self.theme_manager.current_theme)
        self.settings.setValue("notifications_enabled", self.notification_manager.notifications_enabled)
        self.settings.setValue("geometry", self.saveGeometry())

    def ensure_screenshots_dir(self):
        """Ensure screenshots directory exists"""
        if not os.path.exists(self.screenshots_dir):
            os.makedirs(self.screenshots_dir)
    
    def init_ui(self):
        """Initialize the enhanced user interface"""
        self.setWindowTitle("MFFUHijack - Live Scan Monitor Enhanced")
        self.setMinimumSize(1400, 900)
        self.resize(1400, 900)

        # Create menu bar
        self.create_menu_bar()

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Create main sections
        self.create_header_section(main_layout)
        self.create_content_sections(main_layout)
        self.create_footer_section(main_layout)

    def create_menu_bar(self):
        """Create enhanced menu bar"""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu("&File")

        # Export submenu
        export_menu = file_menu.addMenu("&Export Session Data")

        export_csv_action = QAction("Export to &CSV", self)
        export_csv_action.triggered.connect(self.export_session_csv)
        export_menu.addAction(export_csv_action)

        export_json_action = QAction("Export to &JSON", self)
        export_json_action.triggered.connect(self.export_session_json)
        export_menu.addAction(export_json_action)

        file_menu.addSeparator()

        # Screenshot actions
        screenshot_action = QAction("Take &Screenshot", self)
        screenshot_action.setShortcut("Ctrl+S")
        screenshot_action.triggered.connect(self.take_screenshot)
        file_menu.addAction(screenshot_action)

        open_screenshots_action = QAction("Open Screenshots &Folder", self)
        open_screenshots_action.triggered.connect(self.open_screenshots_folder)
        file_menu.addAction(open_screenshots_action)

        file_menu.addSeparator()

        # Session history
        history_action = QAction("View Session &History", self)
        history_action.triggered.connect(self.show_session_history)
        file_menu.addAction(history_action)

        # View menu
        view_menu = menubar.addMenu("&View")

        # Theme submenu
        theme_menu = view_menu.addMenu("&Theme")

        light_theme_action = QAction("&Light Theme", self)
        light_theme_action.triggered.connect(lambda: self.change_theme("light"))
        theme_menu.addAction(light_theme_action)

        dark_theme_action = QAction("&Dark Theme", self)
        dark_theme_action.triggered.connect(lambda: self.change_theme("dark"))
        theme_menu.addAction(dark_theme_action)

        view_menu.addSeparator()

        # Layout options
        fullscreen_action = QAction("&Full Screen", self)
        fullscreen_action.setShortcut("F11")
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)

        reset_layout_action = QAction("&Reset Layout", self)
        reset_layout_action.triggered.connect(self.reset_layout)
        view_menu.addAction(reset_layout_action)

        # Tools menu
        tools_menu = menubar.addMenu("&Tools")

        # Control actions
        pause_action = QAction("&Pause/Resume Monitoring", self)
        pause_action.setShortcut("Space")
        pause_action.triggered.connect(self.toggle_pause)
        tools_menu.addAction(pause_action)

        reset_stats_action = QAction("&Reset Statistics", self)
        reset_stats_action.triggered.connect(self.reset_statistics)
        tools_menu.addAction(reset_stats_action)

        tools_menu.addSeparator()

        # Testing mode (if available)
        if TESTING_MODE_AVAILABLE:
            testing_action = QAction("🧪 &Live Stream Testing Mode", self)
            testing_action.triggered.connect(self.open_testing_mode)
            tools_menu.addAction(testing_action)

            tools_menu.addSeparator()

        # OCR region selector (if available)
        if OCR_REGION_SELECTOR_AVAILABLE:
            ocr_region_action = QAction("🎯 &Manual OCR Region Selection", self)
            ocr_region_action.triggered.connect(self.open_ocr_region_selector)
            tools_menu.addAction(ocr_region_action)

            tools_menu.addSeparator()

        # Smart features (if available)
        if SMART_FEATURES_AVAILABLE:
            smart_menu = tools_menu.addMenu("🧠 &Smart Features")

            temporal_action = QAction("📊 &Temporal Analytics", self)
            temporal_action.triggered.connect(self.show_temporal_dashboard)
            smart_menu.addAction(temporal_action)

            code_history_action = QAction("📚 &Code History", self)
            code_history_action.triggered.connect(self.show_code_history)
            smart_menu.addAction(code_history_action)

            retry_stats_action = QAction("🔄 &Retry Statistics", self)
            retry_stats_action.triggered.connect(self.show_retry_statistics)
            smart_menu.addAction(retry_stats_action)

            smart_menu.addSeparator()

            analytics_action = QAction("📈 &Comprehensive Analytics", self)
            analytics_action.triggered.connect(self.show_comprehensive_analytics)
            smart_menu.addAction(analytics_action)

            tools_menu.addSeparator()

        # Settings
        settings_action = QAction("&Settings", self)
        settings_action.triggered.connect(self.show_settings_dialog)
        tools_menu.addAction(settings_action)

        # Help menu
        help_menu = menubar.addMenu("&Help")

        shortcuts_action = QAction("&Keyboard Shortcuts", self)
        shortcuts_action.triggered.connect(self.show_shortcuts_help)
        help_menu.addAction(shortcuts_action)

        about_action = QAction("&About", self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)

    def setup_shortcuts(self):
        """Setup keyboard shortcuts"""
        # Global shortcuts
        self.addAction(self.create_shortcut("Ctrl+S", self.take_screenshot))
        self.addAction(self.create_shortcut("Ctrl+R", self.reset_zoom))
        self.addAction(self.create_shortcut("Space", self.toggle_pause))
        self.addAction(self.create_shortcut("Ctrl+E", self.export_session_csv))
        self.addAction(self.create_shortcut("F11", self.toggle_fullscreen))
        self.addAction(self.create_shortcut("Ctrl+T", lambda: self.change_theme("dark" if self.theme_manager.current_theme == "light" else "light")))

    def create_shortcut(self, key_sequence: str, callback):
        """Create a keyboard shortcut action"""
        action = QAction(self)
        action.setShortcut(key_sequence)
        action.triggered.connect(callback)
        return action
    
    def create_header_section(self, parent_layout):
        """Create enhanced header section with title and controls"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        header_layout = QHBoxLayout(header_frame)

        # Title with theme-aware styling
        title_label = QLabel("🔍 Live Scan Monitor Enhanced")
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Control buttons
        control_layout = QHBoxLayout()

        # Pause/Resume button
        self.pause_btn = QPushButton("⏸️ Pause")
        self.pause_btn.clicked.connect(self.toggle_pause)
        self.pause_btn.setEnabled(False)
        control_layout.addWidget(self.pause_btn)

        # Screenshot button
        screenshot_btn = QPushButton("📷 Screenshot")
        screenshot_btn.clicked.connect(self.take_screenshot)
        control_layout.addWidget(screenshot_btn)

        # Theme toggle button
        self.theme_btn = QPushButton("🌙 Dark")
        self.theme_btn.clicked.connect(self.toggle_theme)
        control_layout.addWidget(self.theme_btn)

        # Settings button
        settings_btn = QPushButton("⚙️ Settings")
        settings_btn.clicked.connect(self.show_settings_dialog)
        control_layout.addWidget(settings_btn)

        header_layout.addLayout(control_layout)

        # Status indicator
        self.status_indicator = QLabel("⚫ Offline")
        self.status_indicator.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        header_layout.addWidget(self.status_indicator)

        # Close button
        close_btn = QPushButton("✕ Close")
        close_btn.clicked.connect(self.close)
        header_layout.addWidget(close_btn)

        parent_layout.addWidget(header_frame)
    
    def create_content_sections(self, parent_layout):
        """Create enhanced main content sections"""
        # Create main splitter for content
        self.main_splitter = QSplitter(Qt.Orientation.Horizontal)
        self.main_splitter.setChildrenCollapsible(False)

        # Left panel - Stream info and interactive preview
        left_panel = self.create_enhanced_left_panel()
        self.main_splitter.addWidget(left_panel)

        # Right panel - Statistics, performance, and logs
        right_panel = self.create_enhanced_right_panel()
        self.main_splitter.addWidget(right_panel)

        # Set splitter proportions (60% left, 40% right)
        self.main_splitter.setSizes([840, 560])

        # Connect splitter resize to autosave
        self.main_splitter.splitterMoved.connect(self.on_splitter_moved)

        parent_layout.addWidget(self.main_splitter)
    
    def create_footer_section(self, parent_layout):
        """Create footer with session controls"""
        footer_frame = QFrame()
        footer_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        footer_layout = QHBoxLayout(footer_frame)
        
        # Session duration
        self.session_duration_label = QLabel("Session Duration: 00:00:00")
        footer_layout.addWidget(self.session_duration_label)
        
        footer_layout.addStretch()
        
        # Reset statistics button
        reset_btn = QPushButton("🔄 Reset Statistics")
        reset_btn.clicked.connect(self.reset_statistics)
        footer_layout.addWidget(reset_btn)
        
        parent_layout.addWidget(footer_frame)
    
    def apply_styling(self):
        """Apply styling to the window"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 5px;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #ccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)

    def create_enhanced_left_panel(self):
        """Create enhanced left panel with interactive preview"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # Stream Information Section
        stream_info_group = QGroupBox("📺 Stream Information")
        stream_info_layout = QGridLayout(stream_info_group)

        # Stream URL
        stream_info_layout.addWidget(QLabel("Stream URL:"), 0, 0)
        self.stream_url_label = QLabel("Not connected")
        self.stream_url_label.setWordWrap(True)
        stream_info_layout.addWidget(self.stream_url_label, 0, 1)

        # Stream Status
        stream_info_layout.addWidget(QLabel("Status:"), 1, 0)
        self.stream_status_label = QLabel("⚫ Offline")
        stream_info_layout.addWidget(self.stream_status_label, 1, 1)

        # Playback Speed
        stream_info_layout.addWidget(QLabel("Playback Speed:"), 2, 0)
        self.playback_speed_label = QLabel("1.5x")
        stream_info_layout.addWidget(self.playback_speed_label, 2, 1)

        # Elapsed Time
        stream_info_layout.addWidget(QLabel("Elapsed Time:"), 3, 0)
        self.elapsed_time_label = QLabel("00:00:00")
        stream_info_layout.addWidget(self.elapsed_time_label, 3, 1)

        left_layout.addWidget(stream_info_group)

        # Interactive Live Preview Section
        preview_group = QGroupBox("🎥 Interactive Live Preview")
        preview_layout = QVBoxLayout(preview_group)

        # Preview controls
        preview_controls = QHBoxLayout()

        zoom_reset_btn = QPushButton("🔍 Reset Zoom")
        zoom_reset_btn.clicked.connect(self.reset_zoom)
        preview_controls.addWidget(zoom_reset_btn)

        fullscreen_preview_btn = QPushButton("⛶ Fullscreen")
        fullscreen_preview_btn.clicked.connect(self.toggle_preview_fullscreen)
        preview_controls.addWidget(fullscreen_preview_btn)

        preview_controls.addStretch()

        # Manual code entry button
        manual_code_btn = QPushButton("✏️ Manual Code")
        manual_code_btn.clicked.connect(self.show_manual_code_dialog)
        preview_controls.addWidget(manual_code_btn)

        preview_layout.addLayout(preview_controls)

        # Interactive frame preview
        self.frame_preview = InteractivePreviewWidget()
        self.frame_preview.setMinimumSize(500, 350)
        self.frame_preview.region_changed.connect(self.on_region_changed)
        self.frame_preview.screenshot_requested.connect(self.take_screenshot)
        self.frame_preview.zoom_requested.connect(self.on_zoom_requested)
        preview_layout.addWidget(self.frame_preview)

        # Frame info and controls
        frame_info_layout = QGridLayout()

        # Frame info
        frame_info_layout.addWidget(QLabel("Frame:"), 0, 0)
        self.frame_number_label = QLabel("0")
        frame_info_layout.addWidget(self.frame_number_label, 0, 1)

        frame_info_layout.addWidget(QLabel("Timestamp:"), 0, 2)
        self.frame_timestamp_label = QLabel("--:--:--")
        frame_info_layout.addWidget(self.frame_timestamp_label, 0, 3)

        # OCR Region info
        frame_info_layout.addWidget(QLabel("OCR Region:"), 1, 0)
        self.ocr_region_label = QLabel("0%, 67%, 100% × 33%")
        frame_info_layout.addWidget(self.ocr_region_label, 1, 1, 1, 3)

        preview_layout.addLayout(frame_info_layout)

        # Preview help text
        help_text = QLabel("💡 Ctrl+Click: Adjust OCR region | Shift+Click: Zoom | Right-click: Screenshot | Ctrl+Wheel: Zoom")
        help_text.setWordWrap(True)
        help_text.setStyleSheet("color: #666; font-size: 10px; padding: 5px;")
        preview_layout.addWidget(help_text)

        left_layout.addWidget(preview_group)

        return left_widget

    def create_enhanced_right_panel(self):
        """Create enhanced right panel with performance metrics and advanced features"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # Create tabbed interface for better organization
        tab_widget = QTabWidget()

        # Statistics Tab
        stats_tab = self.create_statistics_tab()
        tab_widget.addTab(stats_tab, "📊 Statistics")

        # Performance Tab
        performance_tab = self.create_performance_tab()
        tab_widget.addTab(performance_tab, "⚡ Performance")

        # Activity Log Tab
        activity_tab = self.create_activity_tab()
        tab_widget.addTab(activity_tab, "📝 Activity")

        # Smart Features Tab (if available)
        if SMART_FEATURES_AVAILABLE:
            smart_tab = self.create_smart_features_tab()
            tab_widget.addTab(smart_tab, "🧠 Smart")

        right_layout.addWidget(tab_widget)

        return right_widget

    def create_statistics_tab(self):
        """Create statistics tab"""
        stats_widget = QWidget()
        stats_layout = QVBoxLayout(stats_widget)

        # Code Detection Statistics
        detection_group = QGroupBox("📊 Code Detection Statistics")
        detection_layout = QGridLayout(detection_group)

        # Total codes
        detection_layout.addWidget(QLabel("Total Codes Detected:"), 0, 0)
        self.total_codes_label = QLabel("0")
        self.total_codes_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        detection_layout.addWidget(self.total_codes_label, 0, 1)

        # Valid codes
        detection_layout.addWidget(QLabel("Valid Codes:"), 1, 0)
        self.valid_codes_label = QLabel("0")
        self.valid_codes_label.setStyleSheet("color: green; font-weight: bold;")
        detection_layout.addWidget(self.valid_codes_label, 1, 1)

        # Invalid codes
        detection_layout.addWidget(QLabel("Invalid Codes:"), 2, 0)
        self.invalid_codes_label = QLabel("0")
        self.invalid_codes_label.setStyleSheet("color: red; font-weight: bold;")
        detection_layout.addWidget(self.invalid_codes_label, 2, 1)

        # Success rate
        detection_layout.addWidget(QLabel("Success Rate:"), 3, 0)
        self.success_rate_label = QLabel("0.0%")
        self.success_rate_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        detection_layout.addWidget(self.success_rate_label, 3, 1)

        # Screenshots taken
        detection_layout.addWidget(QLabel("Screenshots Taken:"), 4, 0)
        self.screenshots_label = QLabel("0")
        detection_layout.addWidget(self.screenshots_label, 4, 1)

        stats_layout.addWidget(detection_group)

        # Account Type Tracking
        account_group = QGroupBox("🏷️ Account Type Tracking")
        account_layout = QGridLayout(account_group)

        self.account_type_labels = {}
        account_types = ["Reset", "Starter", "Starter Plus", "Expert", "Free Reset Code"]

        for i, account_type in enumerate(account_types):
            account_layout.addWidget(QLabel(f"{account_type}:"), i, 0)
            label = QLabel("0")
            label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
            self.account_type_labels[account_type] = label
            account_layout.addWidget(label, i, 1)

            # Add percentage
            percentage_label = QLabel("(0%)")
            percentage_label.setStyleSheet("color: #666; font-size: 9px;")
            self.account_type_labels[f"{account_type}_percent"] = percentage_label
            account_layout.addWidget(percentage_label, i, 2)

        stats_layout.addWidget(account_group)

        return stats_widget

    def create_performance_tab(self):
        """Create performance monitoring tab"""
        perf_widget = QWidget()
        perf_layout = QVBoxLayout(perf_widget)

        # Real-time Performance Metrics
        perf_group = QGroupBox("⚡ Real-time Performance Metrics")
        perf_grid = QGridLayout(perf_group)

        # FPS
        perf_grid.addWidget(QLabel("Frames Per Second:"), 0, 0)
        self.fps_label = QLabel("0.0 FPS")
        self.fps_label.setFont(QFont("Arial", 11, QFont.Weight.Bold))
        perf_grid.addWidget(self.fps_label, 0, 1)

        # Processing time
        perf_grid.addWidget(QLabel("Avg Processing Time:"), 1, 0)
        self.processing_time_label = QLabel("0.0 ms")
        perf_grid.addWidget(self.processing_time_label, 1, 1)

        # Memory usage
        perf_grid.addWidget(QLabel("Memory Usage:"), 2, 0)
        self.memory_label = QLabel("0.0 MB")
        perf_grid.addWidget(self.memory_label, 2, 1)

        # CPU usage
        perf_grid.addWidget(QLabel("CPU Usage:"), 3, 0)
        self.cpu_label = QLabel("0.0%")
        perf_grid.addWidget(self.cpu_label, 3, 1)

        # Frames processed
        perf_grid.addWidget(QLabel("Frames Processed:"), 4, 0)
        self.frames_processed_label = QLabel("0")
        perf_grid.addWidget(self.frames_processed_label, 4, 1)

        perf_layout.addWidget(perf_group)

        # Performance History (simple text display)
        history_group = QGroupBox("📈 Performance History")
        history_layout = QVBoxLayout(history_group)

        self.performance_history = QTextEdit()
        self.performance_history.setMaximumHeight(150)
        self.performance_history.setReadOnly(True)
        self.performance_history.setStyleSheet("""
            QTextEdit {
                font-family: 'Courier New', monospace;
                font-size: 9px;
            }
        """)
        history_layout.addWidget(self.performance_history)

        perf_layout.addWidget(history_group)

        return perf_widget

    def create_activity_tab(self):
        """Create activity log tab"""
        activity_widget = QWidget()
        activity_layout = QVBoxLayout(activity_widget)

        # Session Activity Log
        log_group = QGroupBox("📝 Session Activity Log")
        log_layout = QVBoxLayout(log_group)

        # Log controls
        log_controls = QHBoxLayout()

        clear_log_btn = QPushButton("🗑️ Clear Log")
        clear_log_btn.clicked.connect(self.clear_activity_log)
        log_controls.addWidget(clear_log_btn)

        export_log_btn = QPushButton("💾 Export Log")
        export_log_btn.clicked.connect(self.export_activity_log)
        log_controls.addWidget(export_log_btn)

        log_controls.addStretch()

        # Auto-scroll checkbox
        self.auto_scroll_checkbox = QCheckBox("Auto-scroll")
        self.auto_scroll_checkbox.setChecked(True)
        log_controls.addWidget(self.auto_scroll_checkbox)

        log_layout.addLayout(log_controls)

        # Activity log text area
        self.activity_log = QTextEdit()
        self.activity_log.setReadOnly(True)
        self.activity_log.setStyleSheet("""
            QTextEdit {
                font-family: 'Courier New', monospace;
                font-size: 10px;
            }
        """)
        log_layout.addWidget(self.activity_log)

        activity_layout.addWidget(log_group)

        return activity_widget

    def create_smart_features_tab(self):
        """Create smart features tab"""
        smart_widget = QWidget()
        smart_layout = QVBoxLayout(smart_widget)

        # Smart Retry System
        retry_group = QGroupBox("🔄 Smart Retry System")
        retry_layout = QGridLayout(retry_group)

        retry_layout.addWidget(QLabel("Retry Attempts:"), 0, 0)
        self.retry_attempts_label = QLabel("0")
        retry_layout.addWidget(self.retry_attempts_label, 0, 1)

        retry_layout.addWidget(QLabel("Codes Retried:"), 1, 0)
        self.codes_retried_label = QLabel("0")
        retry_layout.addWidget(self.codes_retried_label, 1, 1)

        retry_layout.addWidget(QLabel("Known Patterns:"), 2, 0)
        self.known_patterns_label = QLabel("0")
        retry_layout.addWidget(self.known_patterns_label, 2, 1)

        smart_layout.addWidget(retry_group)

        # Time-based Analysis
        time_group = QGroupBox("⏰ Time-based Analysis")
        time_layout = QGridLayout(time_group)

        time_layout.addWidget(QLabel("Detection Rate:"), 0, 0)
        self.detection_rate_label = QLabel("0.0/hour")
        time_layout.addWidget(self.detection_rate_label, 0, 1)

        time_layout.addWidget(QLabel("Peak Hour:"), 1, 0)
        self.peak_hour_label = QLabel("--")
        time_layout.addWidget(self.peak_hour_label, 1, 1)

        time_layout.addWidget(QLabel("Quiet Period:"), 2, 0)
        self.quiet_period_label = QLabel("--")
        time_layout.addWidget(self.quiet_period_label, 2, 1)

        smart_layout.addWidget(time_group)

        # Code History
        history_group = QGroupBox("📚 Code History")
        history_layout = QGridLayout(history_group)

        history_layout.addWidget(QLabel("Total Codes in DB:"), 0, 0)
        self.total_db_codes_label = QLabel("0")
        history_layout.addWidget(self.total_db_codes_label, 0, 1)

        history_layout.addWidget(QLabel("Session Codes:"), 1, 0)
        self.session_codes_label = QLabel("0")
        history_layout.addWidget(self.session_codes_label, 1, 1)

        history_layout.addWidget(QLabel("Duplicates Blocked:"), 2, 0)
        self.duplicates_blocked_label = QLabel("0")
        history_layout.addWidget(self.duplicates_blocked_label, 2, 1)

        smart_layout.addWidget(history_group)

        # Smart Features Controls
        controls_group = QGroupBox("🎛️ Smart Controls")
        controls_layout = QVBoxLayout(controls_group)

        controls_buttons = QHBoxLayout()

        temporal_btn = QPushButton("📊 Temporal Dashboard")
        temporal_btn.clicked.connect(self.show_temporal_dashboard)
        controls_buttons.addWidget(temporal_btn)

        history_btn = QPushButton("📚 Code History")
        history_btn.clicked.connect(self.show_code_history)
        controls_buttons.addWidget(history_btn)

        controls_layout.addLayout(controls_buttons)

        analytics_btn = QPushButton("📈 Comprehensive Analytics")
        analytics_btn.clicked.connect(self.show_comprehensive_analytics)
        controls_layout.addWidget(analytics_btn)

        smart_layout.addWidget(controls_group)

        smart_layout.addStretch()

        return smart_widget

    # Import enhanced methods from separate files
    def import_enhanced_methods(self):
        """Import enhanced methods from separate files"""
        try:
            from live_scan_monitor_enhanced_methods import LiveScanMonitorEnhancedMethods
            from live_scan_monitor_dialogs import SettingsDialog, SessionHistoryDialog

            # Add methods to this class
            for method_name in dir(LiveScanMonitorEnhancedMethods):
                if not method_name.startswith('_'):
                    method = getattr(LiveScanMonitorEnhancedMethods, method_name)
                    if callable(method):
                        setattr(self, method_name, method.__get__(self, self.__class__))

            # Store dialog classes
            self.SettingsDialog = SettingsDialog
            self.SessionHistoryDialog = SessionHistoryDialog

        except ImportError as e:
            print(f"Warning: Could not import enhanced methods: {e}")

    def __init__(self):
        # Call the original __init__ first
        super().__init__()

        # Import enhanced methods
        self.import_enhanced_methods()

        # Load saved splitter state after UI is initialized
        QTimer.singleShot(100, self.load_splitter_state)  # Small delay to ensure UI is ready

        # Continue with initialization...

    def start_scanning_session(self, stream_url: str):
        """Start a new enhanced scanning session"""
        self.stream_url = stream_url
        self.is_scanning = True
        self.is_paused = False
        self.stream_status = "Live"
        self.statistics.reset()
        self.performance_monitor.reset()

        # Start session history tracking
        self.current_session_id = self.session_history.start_session(stream_url)

        # Start smart features session
        if self.smart_features:
            self.smart_features.start_session(self.current_session_id)

        # Update UI
        self.stream_url_label.setText(stream_url)
        self.stream_status_label.setText("🟢 Live")
        self.status_indicator.setText("🟢 Scanning")
        self.playback_speed_label.setText(f"{self.playback_speed}x")
        self.session_id_label.setText(f"Session ID: {self.current_session_id}")
        self.pause_btn.setEnabled(True)

        # Log session start
        self.log_activity("🚀 Enhanced scanning session started")
        self.log_activity(f"📺 Connected to: {stream_url}")
        self.log_activity(f"⚡ Playback speed: {self.playback_speed}x")
        self.log_activity(f"🆔 Session ID: {self.current_session_id}")

        # Send notification
        self.notification_manager.notify_session_event(
            "start",
            f"Scanning started for {stream_url[:50]}..."
        )

    def stop_scanning_session(self):
        """Stop the current enhanced scanning session"""
        self.is_scanning = False
        self.is_paused = False
        self.stream_status = "Offline"

        # End session history tracking
        if self.current_session_id:
            self.session_history.end_session(self.statistics, self.performance_monitor)

        # Update UI
        self.stream_status_label.setText("⚫ Offline")
        self.status_indicator.setText("⚫ Offline")
        self.pause_btn.setEnabled(False)
        self.pause_btn.setText("⏸️ Pause")

        # Log session end
        duration = self.statistics.get_session_duration()
        self.log_activity(f"⏹️ Scanning session ended (Duration: {self.format_duration(duration)})")
        self.log_activity(f"📊 Final stats: {self.statistics.total_codes_detected} codes detected, {self.statistics.valid_codes_found} valid")
        self.log_activity(f"📷 Screenshots taken: {self.statistics.screenshots_taken}")

        # Send notification
        self.notification_manager.notify_session_event(
            "stop",
            f"Session ended. {self.statistics.valid_codes_found}/{self.statistics.total_codes_detected} valid codes detected."
        )

    def update_frame_preview(self, pixmap: QPixmap):
        """Update the enhanced frame preview"""
        if pixmap and not pixmap.isNull() and not self.is_paused:
            # Record performance
            start_time = time.time()

            # Update the interactive preview
            self.frame_preview.update_frame(pixmap)

            # Update frame info
            self.statistics.update_frame_info()
            self.frame_number_label.setText(str(self.statistics.frames_processed))
            self.frame_timestamp_label.setText(datetime.now().strftime("%H:%M:%S"))

            # Record processing time
            processing_time = time.time() - start_time
            self.performance_monitor.record_frame_processing(processing_time)

            # Update fullscreen preview if open
            if hasattr(self, 'fullscreen_preview') and self.fullscreen_preview:
                preview_widget = self.fullscreen_preview.centralWidget()
                if preview_widget:
                    preview_widget.update_frame(pixmap)

    def on_code_detected(self, code_data: Dict):
        """Handle enhanced code detection event with smart features"""
        # Process through smart features first
        if self.smart_features:
            enhanced_data = self.smart_features.process_code_detection(code_data)
        else:
            enhanced_data = code_data

        # Add to statistics
        self.statistics.add_code_detection(enhanced_data)

        # Send notification
        self.notification_manager.notify_code_detected(enhanced_data)

        # Auto-screenshot if enabled
        if self.settings.value("auto_screenshot", False, type=bool):
            self.take_screenshot()

        # Update temporal dashboard if open
        if self.temporal_dashboard:
            timestamp = enhanced_data.get('detection_timestamp', datetime.now())
            code_type = enhanced_data.get('type', 'Unknown')
            valid = enhanced_data.get('valid', False)
            self.temporal_dashboard.add_detection(timestamp, code_type, valid)

        # Log the detection with smart features info
        code = enhanced_data.get('code', '')
        code_type = enhanced_data.get('type', 'Unknown')
        confidence = enhanced_data.get('confidence', 0)
        is_manual = enhanced_data.get('manual', False)
        is_duplicate = enhanced_data.get('is_duplicate', False)
        should_retry = enhanced_data.get('should_retry', False)
        suggested_code = enhanced_data.get('suggested_code', '')

        # Enhanced logging with smart features
        if self.statistics.detection_events[-1]['valid']:
            icon = "✏️" if is_manual else "✅"
            log_msg = f"{icon} Valid code detected: {code} ({code_type}) - Confidence: {confidence:.2f}"
            if suggested_code and suggested_code != code:
                log_msg += f" | Smart suggestion: {suggested_code}"
        else:
            icon = "✏️" if is_manual else "❌"
            log_msg = f"{icon} Invalid code detected: {code} ({code_type}) - Confidence: {confidence:.2f}"
            if should_retry:
                log_msg += " | Retry suggested"

        if is_duplicate:
            log_msg += " | Duplicate blocked"

        self.log_activity(log_msg)

    def update_display(self):
        """Update all enhanced display elements"""
        if not self.is_scanning:
            return

        # Update statistics display
        self.total_codes_label.setText(str(self.statistics.total_codes_detected))
        self.valid_codes_label.setText(str(self.statistics.valid_codes_found))
        self.invalid_codes_label.setText(str(self.statistics.invalid_codes_found))
        self.success_rate_label.setText(f"{self.statistics.get_success_rate():.1f}%")
        self.screenshots_label.setText(str(self.statistics.screenshots_taken))

        # Update account type counters with percentages
        total_codes = self.statistics.total_codes_detected
        for account_type, count in self.statistics.codes_by_type.items():
            if account_type in self.account_type_labels:
                self.account_type_labels[account_type].setText(str(count))

                # Update percentage
                percentage = (count / total_codes * 100) if total_codes > 0 else 0
                percent_key = f"{account_type}_percent"
                if percent_key in self.account_type_labels:
                    self.account_type_labels[percent_key].setText(f"({percentage:.1f}%)")

        # Update session duration
        duration = self.statistics.get_session_duration()
        self.session_duration_label.setText(f"Session Duration: {self.format_duration(duration)}")
        self.elapsed_time_label.setText(self.format_duration(duration))

        # Update smart features display
        self.update_smart_features_display()

    def update_performance_stats(self):
        """Update performance statistics display"""
        if not self.is_scanning:
            return

        # Record system stats
        self.performance_monitor.record_system_stats()

        # Update performance labels
        self.fps_label.setText(f"{self.performance_monitor.get_fps():.1f} FPS")
        self.processing_time_label.setText(f"{self.performance_monitor.get_avg_processing_time():.1f} ms")
        self.memory_label.setText(f"{self.performance_monitor.get_memory_usage():.1f} MB")
        self.cpu_label.setText(f"{self.performance_monitor.get_cpu_usage():.1f}%")
        self.frames_processed_label.setText(str(self.statistics.frames_processed))

        # Update performance history (simple text log)
        if hasattr(self, 'performance_history'):
            timestamp = datetime.now().strftime("%H:%M:%S")
            perf_line = f"[{timestamp}] FPS: {self.performance_monitor.get_fps():.1f}, " \
                       f"CPU: {self.performance_monitor.get_cpu_usage():.1f}%, " \
                       f"Memory: {self.performance_monitor.get_memory_usage():.1f}MB"

            self.performance_history.append(perf_line)

            # Keep only last 50 lines
            lines = self.performance_history.toPlainText().split('\n')
            if len(lines) > 50:
                self.performance_history.setPlainText('\n'.join(lines[-50:]))

    def log_activity(self, message: str):
        """Add message to enhanced activity log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.activity_log.append(formatted_message)

        # Auto-scroll if enabled
        if hasattr(self, 'auto_scroll_checkbox') and self.auto_scroll_checkbox.isChecked():
            scrollbar = self.activity_log.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

        # Limit log entries
        max_entries = self.settings.value("max_log_entries", 1000, type=int)
        lines = self.activity_log.toPlainText().split('\n')
        if len(lines) > max_entries:
            self.activity_log.setPlainText('\n'.join(lines[-max_entries:]))

    def clear_activity_log(self):
        """Clear the activity log"""
        self.activity_log.clear()
        self.log_activity("📝 Activity log cleared")

    def reset_statistics(self):
        """Reset all statistics"""
        self.statistics.reset()
        self.performance_monitor.reset()
        self.log_activity("🔄 Statistics and performance metrics reset")

    def format_duration(self, duration: timedelta) -> str:
        """Format duration as HH:MM:SS"""
        total_seconds = int(duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    def closeEvent(self, event):
        """Handle enhanced window close event"""
        try:
            # Save settings
            if hasattr(self, 'save_settings'):
                self.save_settings()

            # Stop timers safely
            if hasattr(self, 'update_timer') and self.update_timer:
                self.update_timer.stop()
            if hasattr(self, 'performance_timer') and self.performance_timer:
                self.performance_timer.stop()

            # End current session if active
            if hasattr(self, 'is_scanning') and self.is_scanning:
                if hasattr(self, 'stop_scanning_session'):
                    self.stop_scanning_session()

            # Close fullscreen preview if open
            if hasattr(self, 'fullscreen_preview') and self.fullscreen_preview:
                self.fullscreen_preview.close()

            # Emit signal and accept
            self.window_closed.emit()

        except Exception as e:
            print(f"Error during window close: {e}")
        finally:
            event.accept()

    # Placeholder methods for dialog functionality (implemented in separate files)
    def show_settings_dialog(self):
        """Show settings dialog"""
        if hasattr(self, 'SettingsDialog'):
            dialog = self.SettingsDialog(self)
            dialog.exec()
        else:
            self.log_activity("⚙️ Settings dialog not available")

    def show_session_history(self):
        """Show session history dialog"""
        if hasattr(self, 'SessionHistoryDialog'):
            dialog = self.SessionHistoryDialog(self.session_history, self)
            dialog.exec()
        else:
            self.log_activity("📚 Session history dialog not available")

    def show_shortcuts_help(self):
        """Show keyboard shortcuts help"""
        shortcuts_text = """
        Keyboard Shortcuts:

        Ctrl+S - Take screenshot
        Ctrl+R - Reset zoom
        Space - Pause/Resume monitoring
        Ctrl+E - Export session to CSV
        F11 - Toggle fullscreen
        Ctrl+T - Toggle theme

        Preview Controls:
        Ctrl+Click - Adjust OCR region
        Shift+Click - Zoom to point
        Right-click - Take screenshot
        Ctrl+Wheel - Zoom in/out
        """

        QMessageBox.information(self, "Keyboard Shortcuts", shortcuts_text)

    def show_about_dialog(self):
        """Show about dialog"""
        about_text = """
        MFFUHijack Live Scan Monitor Enhanced
        Version 2.0

        Real-time OCR livestream code detection with advanced monitoring capabilities.

        Features:
        • Interactive OCR region adjustment
        • Real-time performance monitoring
        • Desktop notifications
        • Session history and export
        • Dark/Light themes
        • Screenshot capture
        • Advanced statistics tracking

        © 2024 MFFUHijack Project
        """

        QMessageBox.about(self, "About Live Scan Monitor", about_text)

    def update_smart_features_display(self):
        """Update smart features display elements"""
        if not self.smart_features or not hasattr(self, 'retry_attempts_label'):
            return

        try:
            # Get analytics
            analytics = self.smart_features.get_comprehensive_analytics()

            # Update retry system stats
            retry_stats = analytics.get('retry_system', {})
            self.retry_attempts_label.setText(str(retry_stats.get('total_retry_attempts', 0)))
            self.codes_retried_label.setText(str(retry_stats.get('codes_retried', 0)))
            self.known_patterns_label.setText(str(retry_stats.get('known_patterns', 0)))

            # Update time analysis stats
            time_stats = analytics.get('time_analysis', {})
            self.detection_rate_label.setText(f"{time_stats.get('detection_frequency', 0):.1f}/hour")

            peak_hours = time_stats.get('peak_hours', [])
            self.peak_hour_label.setText(f"{peak_hours[0]}:00" if peak_hours else "--")

            quiet_periods = time_stats.get('quiet_periods', [])
            self.quiet_period_label.setText(f"{quiet_periods[0]}:00" if quiet_periods else "--")

            # Update code history stats
            history_stats = analytics.get('code_history', {})
            self.total_db_codes_label.setText(str(history_stats.get('total_codes', 0)))
            self.session_codes_label.setText(str(history_stats.get('session_codes', 0)))

            # Calculate duplicates blocked (total detected - session codes)
            duplicates_blocked = self.statistics.total_codes_detected - history_stats.get('session_codes', 0)
            self.duplicates_blocked_label.setText(str(max(0, duplicates_blocked)))

        except Exception as e:
            print(f"Error updating smart features display: {e}")

    def show_temporal_dashboard(self):
        """Show temporal analytics dashboard"""
        if not self.smart_features:
            QMessageBox.information(self, "Smart Features", "Smart features are not available.")
            return

        try:
            if not self.temporal_dashboard:
                from temporal_analytics_dashboard import TemporalAnalyticsDashboard
                self.temporal_dashboard = TemporalAnalyticsDashboard()
                self.temporal_dashboard.set_time_analyzer(self.smart_features.time_analyzer)

            self.temporal_dashboard.show()
            self.temporal_dashboard.raise_()
            self.temporal_dashboard.activateWindow()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open temporal dashboard: {e}")

    def show_code_history(self):
        """Show code history dialog"""
        if not self.smart_features:
            QMessageBox.information(self, "Smart Features", "Smart features are not available.")
            return

        try:
            # Create a simple code history dialog
            dialog = QDialog(self)
            dialog.setWindowTitle("Code History")
            dialog.setModal(True)
            dialog.resize(800, 600)

            layout = QVBoxLayout(dialog)

            # Search
            search_layout = QHBoxLayout()
            search_layout.addWidget(QLabel("Search:"))
            search_input = QLineEdit()
            search_layout.addWidget(search_input)
            search_btn = QPushButton("Search")
            search_layout.addWidget(search_btn)
            layout.addLayout(search_layout)

            # Results
            results_tree = QTreeWidget()
            results_tree.setHeaderLabels(["Code", "Type", "Confidence", "Timestamp", "Valid"])
            layout.addWidget(results_tree)

            def search_codes():
                query = search_input.text().strip()
                if not query:
                    return

                results = self.smart_features.code_history.search_codes(query, 100)
                results_tree.clear()

                for result in results:
                    item = QTreeWidgetItem([
                        result['code'],
                        result['type'],
                        f"{result['confidence']:.2f}",
                        result['timestamp'][:19].replace('T', ' '),
                        "✅" if result['valid'] else "❌"
                    ])
                    results_tree.addTopLevelItem(item)

            search_btn.clicked.connect(search_codes)
            search_input.returnPressed.connect(search_codes)

            # Load recent codes initially
            recent_codes = self.smart_features.code_history.search_codes("", 50)
            for result in recent_codes:
                item = QTreeWidgetItem([
                    result['code'],
                    result['type'],
                    f"{result['confidence']:.2f}",
                    result['timestamp'][:19].replace('T', ' '),
                    "✅" if result['valid'] else "❌"
                ])
                results_tree.addTopLevelItem(item)

            # Close button
            close_btn = QPushButton("Close")
            close_btn.clicked.connect(dialog.accept)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to show code history: {e}")

    def show_retry_statistics(self):
        """Show retry statistics dialog"""
        if not self.smart_features:
            QMessageBox.information(self, "Smart Features", "Smart features are not available.")
            return

        try:
            stats = self.smart_features.retry_system.get_retry_statistics()

            stats_text = f"""
Smart Retry System Statistics:

Total Retry Attempts: {stats.get('total_retry_attempts', 0)}
Codes Retried: {stats.get('codes_retried', 0)}
Average Retries per Code: {stats.get('average_retries_per_code', 0):.1f}
Known Patterns: {stats.get('known_patterns', 0)}
Max Retries per Code: {stats.get('max_retries', 3)}

The smart retry system automatically detects potential OCR errors
and suggests corrections for common character confusions like:
• S ↔ 5
• O ↔ 0
• I ↔ 1
• And many more...
            """

            QMessageBox.information(self, "Retry Statistics", stats_text.strip())

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to show retry statistics: {e}")

    def show_comprehensive_analytics(self):
        """Show comprehensive analytics dialog"""
        if not self.smart_features:
            QMessageBox.information(self, "Smart Features", "Smart features are not available.")
            return

        try:
            analytics = self.smart_features.get_comprehensive_analytics()

            # Create analytics dialog
            dialog = QDialog(self)
            dialog.setWindowTitle("Comprehensive Analytics")
            dialog.setModal(True)
            dialog.resize(600, 500)

            layout = QVBoxLayout(dialog)

            # Analytics text
            analytics_text = QTextEdit()
            analytics_text.setReadOnly(True)

            # Format analytics data
            formatted_text = "🧠 SMART FEATURES COMPREHENSIVE ANALYTICS\n"
            formatted_text += "=" * 50 + "\n\n"

            # Time Analysis
            time_stats = analytics.get('time_analysis', {})
            formatted_text += "⏰ TIME-BASED ANALYSIS:\n"
            formatted_text += f"• Total Detections: {time_stats.get('total_detections', 0)}\n"
            formatted_text += f"• Detection Frequency: {time_stats.get('detection_frequency', 0):.1f} per hour\n"
            formatted_text += f"• Average Interval: {time_stats.get('average_interval', 0):.1f} seconds\n"
            formatted_text += f"• Peak Hours: {', '.join([f'{h}:00' for h in time_stats.get('peak_hours', [])])}\n"
            formatted_text += f"• Quiet Periods: {', '.join([f'{h}:00' for h in time_stats.get('quiet_periods', [])])}\n\n"

            # Code History
            history_stats = analytics.get('code_history', {})
            formatted_text += "📚 CODE HISTORY:\n"
            formatted_text += f"• Total Codes in Database: {history_stats.get('total_codes', 0)}\n"
            formatted_text += f"• Valid Codes: {history_stats.get('valid_codes', 0)}\n"
            formatted_text += f"• Invalid Codes: {history_stats.get('invalid_codes', 0)}\n"
            formatted_text += f"• Success Rate: {history_stats.get('success_rate', 0):.1f}%\n"
            formatted_text += f"• Session Codes: {history_stats.get('session_codes', 0)}\n\n"

            # Retry System
            retry_stats = analytics.get('retry_system', {})
            formatted_text += "🔄 SMART RETRY SYSTEM:\n"
            formatted_text += f"• Total Retry Attempts: {retry_stats.get('total_retry_attempts', 0)}\n"
            formatted_text += f"• Codes Retried: {retry_stats.get('codes_retried', 0)}\n"
            formatted_text += f"• Average Retries per Code: {retry_stats.get('average_retries_per_code', 0):.1f}\n"
            formatted_text += f"• Known Patterns: {retry_stats.get('known_patterns', 0)}\n\n"

            # Session Info
            formatted_text += "📋 SESSION INFO:\n"
            formatted_text += f"• Session ID: {analytics.get('session_id', 'N/A')}\n"
            formatted_text += f"• Analysis Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

            analytics_text.setPlainText(formatted_text)
            layout.addWidget(analytics_text)

            # Export button
            export_btn = QPushButton("💾 Export Analytics")
            export_btn.clicked.connect(lambda: self.export_analytics(analytics))
            layout.addWidget(export_btn)

            # Close button
            close_btn = QPushButton("Close")
            close_btn.clicked.connect(dialog.accept)
            layout.addWidget(close_btn)

            dialog.exec()

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to show comprehensive analytics: {e}")

    def export_analytics(self, analytics: Dict):
        """Export analytics to JSON file"""
        try:
            filename, _ = QFileDialog.getSaveFileName(
                self, "Export Analytics",
                f"smart_analytics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON Files (*.json)"
            )

            if filename:
                with open(filename, 'w') as f:
                    json.dump(analytics, f, indent=2, default=str)

                QMessageBox.information(self, "Export Successful",
                                      f"Analytics exported to {filename}")

        except Exception as e:
            QMessageBox.critical(self, "Export Failed", f"Failed to export analytics: {e}")

    def open_testing_mode(self):
        """Open the Live Stream Testing Mode window"""
        if not TESTING_MODE_AVAILABLE:
            QMessageBox.information(self, "Testing Mode", "Live Stream Testing Mode is not available.")
            return

        try:
            if not self.testing_window:
                self.testing_window = LiveStreamTestingWindow()

                # Position the testing window
                geometry = self.geometry()
                self.testing_window.setGeometry(
                    geometry.x() + 50,
                    geometry.y() + 50,
                    1200, 800
                )

            self.testing_window.show()
            self.testing_window.raise_()
            self.testing_window.activateWindow()

            self.log_activity("🧪 Live Stream Testing Mode opened")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open testing mode: {e}")
            self.log_activity(f"❌ Failed to open testing mode: {e}")

    def open_ocr_region_selector(self):
        """Open the manual OCR region selector"""
        if not OCR_REGION_SELECTOR_AVAILABLE:
            QMessageBox.information(self, "OCR Region Selector", "Manual OCR region selector is not available.")
            return

        try:
            # Get current OCR region settings (if any)
            current_region = getattr(self, 'current_ocr_region', (0.0, 67.0, 100.0, 33.0))

            # Open the selector dialog
            dialog = OCRRegionSelectorDialog(self, initial_region=current_region)

            if dialog.exec() == QDialog.DialogCode.Accepted:
                # Get the selected region
                selected_region = dialog.get_selected_region()
                self.current_ocr_region = selected_region

                # Update OCR preview if it exists
                if hasattr(self, 'ocr_preview') and self.ocr_preview:
                    self.update_ocr_preview_region(selected_region)

                # Log the change
                x, y, w, h = selected_region
                self.log_activity(f"🎯 OCR region updated: X:{x:.1f}%, Y:{y:.1f}%, W:{w:.1f}%, H:{h:.1f}%")

                # Save settings
                self.save_ocr_region_settings(selected_region)

                QMessageBox.information(self, "OCR Region Updated",
                                      f"OCR region has been updated successfully!\n\n"
                                      f"Position: {x:.1f}%, {y:.1f}%\n"
                                      f"Size: {w:.1f}% × {h:.1f}%")
            else:
                self.log_activity("🎯 OCR region selection cancelled")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open OCR region selector: {e}")
            self.log_activity(f"❌ Failed to open OCR region selector: {e}")

    def update_ocr_preview_region(self, region: tuple):
        """Update the OCR preview region"""
        try:
            x_percent, y_percent, w_percent, h_percent = region

            # Update the OCR preview widget if it exists
            if hasattr(self, 'ocr_preview') and self.ocr_preview:
                # This would update the red box overlay on the preview
                # Implementation depends on the specific OCR preview widget
                self.log_activity(f"🎯 OCR preview region updated to {x_percent:.1f}%, {y_percent:.1f}%, {w_percent:.1f}%, {h_percent:.1f}%")

        except Exception as e:
            self.log_activity(f"❌ Failed to update OCR preview region: {e}")

    def save_ocr_region_settings(self, region: tuple):
        """Save OCR region settings to preferences"""
        try:
            x, y, w, h = region
            settings = QSettings()
            settings.setValue("ocr_region_x", x)
            settings.setValue("ocr_region_y", y)
            settings.setValue("ocr_region_width", w)
            settings.setValue("ocr_region_height", h)

            self.log_activity("💾 OCR region settings saved")

        except Exception as e:
            self.log_activity(f"❌ Failed to save OCR region settings: {e}")

    def load_ocr_region_settings(self) -> tuple:
        """Load OCR region settings from preferences"""
        try:
            settings = QSettings()
            x = settings.value("ocr_region_x", 0.0, type=float)
            y = settings.value("ocr_region_y", 66.9, type=float)
            w = settings.value("ocr_region_width", 100.0, type=float)
            h = settings.value("ocr_region_height", 32.9, type=float)

            return (x, y, w, h)

        except Exception as e:
            self.log_activity(f"❌ Failed to load OCR region settings: {e}")
            return (0.0, 66.9, 100.0, 32.9)  # Default region

    def start_scanning_session(self, stream_url: str):
        """Start a new scanning session"""
        self.stream_url = stream_url
        self.is_scanning = True
        self.stream_status = "Live"
        self.statistics.reset()

        # Update UI
        self.stream_url_label.setText(stream_url)
        self.stream_status_label.setText("🟢 Live")
        self.status_indicator.setText("🟢 Scanning")
        self.playback_speed_label.setText(f"{self.playback_speed}x")

        # Log session start
        self.log_activity("🚀 Scanning session started")
        self.log_activity(f"📺 Connected to: {stream_url}")
        self.log_activity(f"⚡ Playback speed: {self.playback_speed}x")

    def stop_scanning_session(self):
        """Stop the current scanning session"""
        self.is_scanning = False
        self.stream_status = "Offline"

        # Update UI
        self.stream_status_label.setText("⚫ Offline")
        self.status_indicator.setText("⚫ Offline")

        # Log session end
        duration = self.statistics.get_session_duration()
        self.log_activity(f"⏹️ Scanning session ended (Duration: {self.format_duration(duration)})")
        self.log_activity(f"📊 Final stats: {self.statistics.total_codes_detected} codes detected, {self.statistics.valid_codes_found} valid")

    def update_frame_preview(self, pixmap: QPixmap):
        """Update the frame preview with OCR region overlay"""
        if pixmap and not pixmap.isNull():
            # Scale pixmap to fit preview area
            scaled_pixmap = pixmap.scaled(
                self.frame_preview.size(),
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation
            )

            # Draw OCR region overlay (red box)
            painter = QPainter(scaled_pixmap)
            painter.setPen(QPen(QColor(255, 0, 0), 2))

            # Calculate OCR region coordinates (assuming region is stored as percentages)
            # This would need to be connected to the actual OCR region settings
            width = scaled_pixmap.width()
            height = scaled_pixmap.height()

            # Default region (bottom third) - this should be updated from actual settings
            x = int(width * 0.0)  # 0% from left
            y = int(height * 0.67)  # 67% from top
            w = int(width * 1.0)  # 100% width
            h = int(height * 0.33)  # 33% height

            painter.drawRect(x, y, w, h)
            painter.end()

            self.frame_preview.setPixmap(scaled_pixmap)

            # Update frame info
            self.statistics.update_frame_info()
            self.frame_number_label.setText(str(self.statistics.frames_processed))
            self.frame_timestamp_label.setText(datetime.now().strftime("%H:%M:%S"))

    def on_code_detected(self, code_data: Dict):
        """Handle code detection event"""
        self.statistics.add_code_detection(code_data)

        # Log the detection
        code = code_data.get('code', '')
        code_type = code_data.get('type', 'Unknown')
        confidence = code_data.get('confidence', 0)

        if self.statistics.detection_events[-1]['valid']:
            self.log_activity(f"✅ Valid code detected: {code} ({code_type}) - Confidence: {confidence:.2f}")
        else:
            self.log_activity(f"❌ Invalid code detected: {code} ({code_type}) - Confidence: {confidence:.2f}")

    def update_display(self):
        """Update all display elements"""
        if not self.is_scanning:
            return

        # Update statistics display
        self.total_codes_label.setText(str(self.statistics.total_codes_detected))
        self.valid_codes_label.setText(str(self.statistics.valid_codes_found))
        self.invalid_codes_label.setText(str(self.statistics.invalid_codes_found))
        self.success_rate_label.setText(f"{self.statistics.get_success_rate():.1f}%")

        # Update account type counters
        for account_type, count in self.statistics.codes_by_type.items():
            if account_type in self.account_type_labels:
                self.account_type_labels[account_type].setText(str(count))

        # Update session duration
        duration = self.statistics.get_session_duration()
        self.session_duration_label.setText(f"Session Duration: {self.format_duration(duration)}")
        self.elapsed_time_label.setText(self.format_duration(duration))

    def log_activity(self, message: str):
        """Add message to activity log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.activity_log.append(formatted_message)

        # Auto-scroll to bottom
        scrollbar = self.activity_log.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def clear_activity_log(self):
        """Clear the activity log"""
        self.activity_log.clear()
        self.log_activity("📝 Activity log cleared")

    def reset_statistics(self):
        """Reset all statistics"""
        self.statistics.reset()
        self.log_activity("🔄 Statistics reset")

    def format_duration(self, duration: timedelta) -> str:
        """Format duration as HH:MM:SS"""
        total_seconds = int(duration.total_seconds())
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"


# Test function for standalone testing
if __name__ == "__main__":
    from PyQt6.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # Create and show the monitor window
    monitor = LiveScanMonitorWindow()
    monitor.show()

    # Simulate starting a scanning session
    monitor.start_scanning_session("https://www.youtube.com/@MyFundedFuturesPropFirm")

    # Simulate some code detections for testing
    import threading
    import time

    def simulate_detections():
        time.sleep(2)
        monitor.on_code_detected({
            'code': 'ABC123',
            'type': 'Starter',
            'full_text': 'FREE 50 STARTER: ABC123',
            'confidence': 0.95
        })

        time.sleep(3)
        monitor.on_code_detected({
            'code': 'XYZ789',
            'type': 'Expert',
            'full_text': 'EXPERT ACCOUNT: XYZ789',
            'confidence': 0.87
        })

    # Start simulation in background
    threading.Thread(target=simulate_detections, daemon=True).start()

    sys.exit(app.exec())
