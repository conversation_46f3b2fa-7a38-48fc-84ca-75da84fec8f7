#!/usr/bin/env python3
"""
Final comprehensive test for Tesseract OCR integration
"""

import sys
import os
import numpy as np
import cv2

# Add support folder to path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'MFFUHijack_Support'))

def create_livestream_test_image():
    """Create a realistic livestream-style test image"""
    # Create image similar to livestream dimensions
    img = np.ones((300, 800, 3), dtype=np.uint8) * 240  # Light gray background
    
    # Add typical livestream text patterns
    texts = [
        ("FREE 50 STARTER ACCOUNTS", (50, 180)),
        ("USE CODE: START123", (50, 220)),
        ("x5 FREE RESETS", (50, 260)),
        ("USE CODE: RESET5J", (400, 260))
    ]
    
    for text, pos in texts:
        # Use bold text similar to livestreams
        cv2.putText(img, text, pos, cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    
    return img

def test_ocr_manager_integration():
    """Test full OCR manager integration with Tesseract"""
    print("🔧 Testing OCR Manager with Tesseract...")
    
    try:
        from ocr_utils import ocr_manager
        
        print(f"📊 Available engines: {list(ocr_manager.engines.keys())}")
        print(f"📊 Current engine: {ocr_manager.current_engine}")
        
        # Switch to Tesseract
        if ocr_manager.set_engine('Tesseract'):
            print("✅ Successfully switched to Tesseract")
        else:
            print("❌ Failed to switch to Tesseract")
            return False
        
        # Create test image
        test_image = create_livestream_test_image()
        print(f"📊 Created test image: {test_image.shape}")
        
        # Test text extraction
        print("🔍 Extracting text from livestream-style image...")
        results = ocr_manager.extract_text_from_image(test_image, region="bottom_third")
        
        print(f"📊 Found {len(results)} text regions:")
        for i, result in enumerate(results):
            text = result.get('text', '')
            confidence = result.get('confidence', 0)
            print(f"   {i+1}. '{text}' (confidence: {confidence:.3f})")
        
        # Test code detection
        print("\n🎯 Testing code detection...")
        codes = ocr_manager.process_frame_for_codes(test_image, region="bottom_third")
        
        print(f"📊 Detected codes: {len(codes)}")
        for i, code in enumerate(codes):
            print(f"   {i+1}. {code}")
        
        success = len(results) > 0
        if success:
            print("✅ OCR Manager integration working!")
        else:
            print("❌ OCR Manager integration failed")
        
        return success
        
    except Exception as e:
        print(f"❌ OCR Manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comparison_with_easyocr():
    """Compare Tesseract vs EasyOCR performance"""
    print("\n⚖️ Comparing Tesseract vs EasyOCR...")
    
    try:
        from ocr_utils import ocr_manager
        import time
        
        # Create test image
        test_image = create_livestream_test_image()
        
        engines_to_test = ['Tesseract', 'EasyOCR']
        results_comparison = {}
        
        for engine_name in engines_to_test:
            if engine_name in ocr_manager.engines and ocr_manager.engines[engine_name].is_available():
                print(f"\n🔍 Testing {engine_name}...")
                
                if ocr_manager.set_engine(engine_name):
                    start_time = time.time()
                    results = ocr_manager.extract_text_from_image(test_image, region="bottom_third")
                    end_time = time.time()
                    
                    processing_time = end_time - start_time
                    
                    results_comparison[engine_name] = {
                        'results': results,
                        'time': processing_time,
                        'text_count': len(results)
                    }
                    
                    print(f"   📊 {engine_name}: {len(results)} results in {processing_time:.3f}s")
                    for result in results[:3]:  # Show first 3 results
                        text = result.get('text', '')
                        confidence = result.get('confidence', 0)
                        print(f"      '{text}' (conf: {confidence:.3f})")
                    
                    if len(results) > 3:
                        print(f"      ... and {len(results) - 3} more results")
                else:
                    print(f"   ❌ Failed to switch to {engine_name}")
        
        # Summary comparison
        print(f"\n📋 Performance Comparison:")
        for engine_name, data in results_comparison.items():
            print(f"   {engine_name}: {data['text_count']} results, {data['time']:.3f}s")
        
        return len(results_comparison) > 0
        
    except Exception as e:
        print(f"❌ Comparison test failed: {e}")
        return False

def test_different_regions():
    """Test OCR with different image regions"""
    print("\n📍 Testing different OCR regions...")
    
    try:
        from ocr_utils import ocr_manager
        
        # Make sure we're using Tesseract
        ocr_manager.set_engine('Tesseract')
        
        # Create test image
        test_image = create_livestream_test_image()
        
        regions = ['full', 'bottom_third', 'bottom_half', 'center']
        
        for region in regions:
            print(f"\n🔍 Testing region: {region}")
            results = ocr_manager.extract_text_from_image(test_image, region=region)
            
            print(f"   📊 Found {len(results)} text elements")
            for result in results[:2]:  # Show first 2 results
                text = result.get('text', '')
                confidence = result.get('confidence', 0)
                print(f"      '{text}' (conf: {confidence:.3f})")
        
        print("✅ Region testing completed")
        return True
        
    except Exception as e:
        print(f"❌ Region test failed: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("🧪 TESSERACT OCR FINAL TEST")
    print("=" * 60)
    
    tests = [
        ("OCR Manager Integration", test_ocr_manager_integration),
        ("Tesseract vs EasyOCR", test_comparison_with_easyocr),
        ("Different Regions", test_different_regions),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
    
    print(f"\n{'='*60}")
    print(f"🏁 FINAL TEST RESULTS: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("✅ Tesseract OCR is fully functional!")
        print("🎯 Ready for use in MFFUHijack livestream detection")
    elif passed > 0:
        print("⚠️ Tesseract partially working - some issues remain")
    else:
        print("❌ Tesseract OCR integration has major issues")
    
    print("="*60)
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
