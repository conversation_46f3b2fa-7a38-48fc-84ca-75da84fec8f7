@echo off
title MFFUHijack Console Scanner - Install Dependencies
color 0B

echo.
echo ===============================================================================
echo                    MFFUHIJACK CONSOLE SCANNER - INSTALLER
echo ===============================================================================
echo.
echo This script will install the required Python packages for MFFUHijack Console Scanner.
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8+ from: https://www.python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo Python version:
python --version
echo.

REM Check if pip is available
pip --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: pip is not available
    echo Please reinstall Python with pip included
    echo.
    pause
    exit /b 1
)

echo Installing Python packages...
echo.

REM Install packages from requirements.txt
if exist "requirements.txt" (
    pip install -r requirements.txt
) else (
    echo Installing packages individually...
    pip install pytesseract>=0.3.10
    pip install opencv-python>=4.8.0
    pip install Pillow>=10.0.0
    pip install numpy>=1.24.0
)

if errorlevel 1 (
    echo.
    echo ERROR: Failed to install some packages
    echo Please check the error messages above and try again
    echo.
    pause
    exit /b 1
)

echo.
echo ===============================================================================
echo Python packages installed successfully!
echo ===============================================================================
echo.
echo IMPORTANT: You still need to install Tesseract OCR binary separately:
echo.
echo For Windows:
echo 1. Download from: https://github.com/UB-Mannheim/tesseract/wiki
echo 2. Install to: C:\Program Files\Tesseract-OCR\
echo 3. The application will automatically detect it
echo.
echo For other operating systems:
echo - macOS: brew install tesseract
echo - Linux: sudo apt install tesseract-ocr
echo.
echo After installing Tesseract, run: test_console.bat to verify everything works
echo Then run: run_mffuhijack_console.bat to start the application
echo.
pause
