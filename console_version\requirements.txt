# MFFUHijack Console Scanner Requirements
# Install with: pip install -r requirements.txt

# Core OCR Engine
pytesseract>=0.3.10

# Image Processing
opencv-python>=4.8.0
Pillow>=10.0.0
numpy>=1.24.0

# Browser Automation (optional but recommended)
selenium>=4.15.0

# GUI Integration (optional - for credential import)
PyQt6>=6.5.0

# Note: Additional requirements
# - Tesseract binary must be installed separately
#   Windows: https://github.com/UB-Mannheim/tesseract/wiki
#   macOS: brew install tesseract
#   Linux: sudo apt install tesseract-ocr
# - Chrome browser required for browser automation
