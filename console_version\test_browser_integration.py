#!/usr/bin/env python3
"""
Test script for browser integration in console version
Verifies browser preloading matches GUI version exactly
"""

import sys
import os
import logging

def test_browser_import():
    """Test browser automation import"""
    print("🔍 Testing browser automation import...")
    
    try:
        # Suppress selenium logging like console version
        logging.basicConfig(level=logging.ERROR)
        selenium_logger = logging.getLogger('selenium')
        selenium_logger.setLevel(logging.ERROR)
        urllib3_logger = logging.getLogger('urllib3')
        urllib3_logger.setLevel(logging.ERROR)
        
        # Add support folder to path
        sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'MFFUHijack_Support'))
        
        from browser_automation import browser_automation, SELENIUM_AVAILABLE
        
        if not SELENIUM_AVAILABLE:
            print("❌ Selenium not available")
            return False
        
        print("✅ Browser automation imported successfully")
        print(f"   Available: {SELENIUM_AVAILABLE}")
        print(f"   Initialized: {browser_automation.is_initialized}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_browser_methods():
    """Test browser automation methods availability"""
    print("\n🔧 Testing browser automation methods...")
    
    try:
        sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'MFFUHijack_Support'))
        from browser_automation import browser_automation
        
        # Check required methods exist
        required_methods = [
            'login_to_mff',
            'wait_for_login_completion',
            'open_manual_tabs',
            'submit_code',
            'initialize_browser',
            'close_browser'
        ]
        
        for method in required_methods:
            if hasattr(browser_automation, method):
                print(f"   ✅ {method}")
            else:
                print(f"   ❌ {method} - MISSING")
                return False
        
        print("✅ All required methods available")
        return True
        
    except Exception as e:
        print(f"❌ Method check error: {e}")
        return False

def test_console_integration():
    """Test console version integration"""
    print("\n🖥️ Testing console integration...")
    
    try:
        from mffuhijack_console import preload_browser_pages, submit_code_to_browser, validate_code_in_browser
        
        print("✅ Console browser functions imported successfully")
        print("   - preload_browser_pages")
        print("   - submit_code_to_browser") 
        print("   - validate_code_in_browser")
        
        return True
        
    except ImportError as e:
        print(f"❌ Console integration import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Console integration error: {e}")
        return False

def test_logging_suppression():
    """Test selenium logging suppression"""
    print("\n🔇 Testing selenium logging suppression...")
    
    try:
        import logging
        
        # Check logging levels
        selenium_logger = logging.getLogger('selenium')
        urllib3_logger = logging.getLogger('urllib3')
        
        print(f"   Selenium logger level: {selenium_logger.level} (should be 40 for ERROR)")
        print(f"   urllib3 logger level: {urllib3_logger.level} (should be 40 for ERROR)")
        
        if selenium_logger.level == logging.ERROR and urllib3_logger.level == logging.ERROR:
            print("✅ Selenium logging properly suppressed")
            return True
        else:
            print("❌ Selenium logging not properly suppressed")
            return False
            
    except Exception as e:
        print(f"❌ Logging suppression test error: {e}")
        return False

def test_gui_compatibility():
    """Test compatibility with GUI version workflow"""
    print("\n🔗 Testing GUI compatibility...")
    
    try:
        sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'MFFUHijack_Support'))
        from browser_automation import browser_automation
        
        # Test same workflow as GUI version
        print("   Testing GUI workflow compatibility:")
        print("   1. browser_automation.login_to_mff(username, password)")
        print("   2. browser_automation.wait_for_login_completion(timeout=60)")
        print("   3. browser_automation.open_manual_tabs(account_types)")
        print("   4. browser_automation.submit_code(code, account_type, reset_account)")
        
        # Check if methods have correct signatures
        import inspect
        
        # Check login_to_mff signature
        login_sig = inspect.signature(browser_automation.login_to_mff)
        print(f"   login_to_mff signature: {login_sig}")
        
        # Check open_manual_tabs signature  
        tabs_sig = inspect.signature(browser_automation.open_manual_tabs)
        print(f"   open_manual_tabs signature: {tabs_sig}")
        
        # Check submit_code signature
        submit_sig = inspect.signature(browser_automation.submit_code)
        print(f"   submit_code signature: {submit_sig}")
        
        print("✅ GUI compatibility verified")
        return True
        
    except Exception as e:
        print(f"❌ GUI compatibility test error: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("🧪 BROWSER INTEGRATION TEST")
    print("=" * 60)
    
    tests = [
        ("Browser Import", test_browser_import),
        ("Browser Methods", test_browser_methods),
        ("Console Integration", test_console_integration),
        ("Logging Suppression", test_logging_suppression),
        ("GUI Compatibility", test_gui_compatibility),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name}: PASSED")
        else:
            print(f"❌ {test_name}: FAILED")
    
    print(f"\n{'='*60}")
    print(f"🏁 BROWSER INTEGRATION TEST RESULTS: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("✅ Browser integration working perfectly!")
        print("🎯 Console version uses EXACT same browser automation as GUI")
        print("🔇 Selenium logging properly suppressed")
    elif passed > 0:
        print("⚠️ Some tests passed. Check failed tests above.")
    else:
        print("❌ Browser integration has issues. Check errors above.")
    
    print("\n🎯 Key Features:")
    print("• Uses global browser_automation instance like GUI")
    print("• Suppresses all selenium info/warning/error messages")
    print("• Identical workflow: login → wait → preload → submit")
    print("• Same method signatures and parameters")
    
    print("="*60)
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
