#!/usr/bin/env python3
"""
Test script to verify browser preloading speed optimizations
"""

import sys
import time
from pathlib import Path

# Add support directory to path
sys.path.append(str(Path(__file__).parent / "MFFUHijack_Support"))

def test_fast_element_methods():
    """Test that fast element finding methods exist"""
    print("🧪 Testing Fast Element Methods...")
    print("=" * 40)
    
    try:
        from browser_automation import BrowserAutomation
        
        # Create instance
        browser = BrowserAutomation()
        
        # Check if fast methods exist
        fast_methods = [
            '_find_element_fast',
            '_find_clickable_element_fast'
        ]
        
        all_exist = True
        for method_name in fast_methods:
            if hasattr(browser, method_name):
                print(f"✅ {method_name} method exists")
                
                # Check if it's callable
                if callable(getattr(browser, method_name)):
                    print(f"✅ {method_name} method is callable")
                else:
                    print(f"❌ {method_name} exists but is not callable")
                    all_exist = False
            else:
                print(f"❌ {method_name} method does not exist")
                all_exist = False
        
        return all_exist
        
    except Exception as e:
        print(f"❌ Error testing fast element methods: {e}")
        return False

def test_timeout_optimizations():
    """Test that timeout values have been optimized"""
    print("\n🧪 Testing Timeout Optimizations...")
    print("=" * 40)
    
    try:
        # Read the browser automation file and check for optimized timeouts
        browser_file = Path("MFFUHijack_Support/browser_automation.py")
        if not browser_file.exists():
            print("⚠️  Browser automation file not found")
            return False
            
        content = browser_file.read_text(encoding='utf-8')
        
        # Check for fast timeout values
        optimizations_found = 0
        
        if "timeout=0.3" in content:
            print("✅ Ultra-fast timeout (0.3s) found")
            optimizations_found += 1
            
        if "timeout=0.5" in content:
            print("✅ Fast timeout (0.5s) found")
            optimizations_found += 1
            
        if "timeout=1.0" in content:
            print("✅ Quick timeout (1.0s) found")
            optimizations_found += 1
            
        if "_find_element_fast" in content:
            print("✅ Fast element finding method implemented")
            optimizations_found += 1
            
        if "_find_clickable_element_fast" in content:
            print("✅ Fast clickable element finding method implemented")
            optimizations_found += 1
            
        if "_fast_click" in content:
            print("✅ Fast click method being used")
            optimizations_found += 1
            
        if optimizations_found >= 4:
            print(f"✅ Found {optimizations_found} speed optimizations")
            return True
        else:
            print(f"⚠️  Only found {optimizations_found} optimizations (expected 4+)")
            return False
            
    except Exception as e:
        print(f"❌ Error testing timeout optimizations: {e}")
        return False

def test_coupon_button_optimization():
    """Test that coupon button detection has been optimized"""
    print("\n🧪 Testing Coupon Button Optimization...")
    print("=" * 40)
    
    try:
        # Read the browser automation file and check for coupon optimizations
        browser_file = Path("MFFUHijack_Support/browser_automation.py")
        if not browser_file.exists():
            print("⚠️  Browser automation file not found")
            return False
            
        content = browser_file.read_text(encoding='utf-8')
        
        # Check for specific optimizations
        optimizations = [
            ("Ultra-fast coupon setup", "Ultra-fast coupon setup" in content),
            ("Fast-path textbox check", "Fast-path: Check if coupon textbox" in content),
            ("Minimal coupon selectors", "Only try coupon button if textbox not found" in content),
            ("Fast click usage", "_fast_click(coupon_button)" in content),
            ("Reduced selector list", "Add coupon code" in content and "Add coupon" in content)
        ]
        
        passed = 0
        for desc, check in optimizations:
            if check:
                print(f"✅ {desc}: Found")
                passed += 1
            else:
                print(f"❌ {desc}: Not found")
        
        if passed >= 3:
            print(f"✅ Coupon button optimization successful ({passed}/5 checks passed)")
            return True
        else:
            print(f"❌ Coupon button optimization incomplete ({passed}/5 checks passed)")
            return False
            
    except Exception as e:
        print(f"❌ Error testing coupon button optimization: {e}")
        return False

def main():
    """Run all speed optimization tests"""
    print("🚀 Browser Preloading Speed Optimization Test")
    print("=" * 60)
    
    tests = [
        ("Fast Element Methods", test_fast_element_methods),
        ("Timeout Optimizations", test_timeout_optimizations),
        ("Coupon Button Optimization", test_coupon_button_optimization)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}: EXCEPTION - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All speed optimizations verified!")
        print("\n✅ Optimizations Applied:")
        print("   • Ultra-fast element finding methods (0.3-1.0s timeouts)")
        print("   • Fast-path coupon textbox detection")
        print("   • Reduced selector lists for faster searching")
        print("   • Fast click methods instead of enhanced clicks")
        print("   • Minimal retry logic to avoid delays")
        print("\n🚀 Browser preloading should now be significantly faster!")
        return True
    else:
        print("⚠️  Some optimizations may be missing. Check the output above.")
        return False

if __name__ == "__main__":
    main()
