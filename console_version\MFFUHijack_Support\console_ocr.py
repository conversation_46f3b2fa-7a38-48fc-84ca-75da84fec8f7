"""
Console OCR Module for MFFUHijack
Tesseract-only OCR implementation for console scanner
Optimized for performance and reliability
"""

import os
import platform
import numpy as np
import cv2
from typing import List, Dict, Any, Optional, Tuple
import re

try:
    import pytesseract
    from PIL import Image
    TESSERACT_AVAILABLE = True
except ImportError:
    pytesseract = None
    TESSERACT_AVAILABLE = False

class ConsoleTesseractOCR:
    """Tesseract OCR engine optimized for console scanning"""
    
    def __init__(self):
        self.name = "Tesseract"
        self.setup_tesseract()
        self.account_patterns = {
            'Starter': [
                r'STARTER\s+ACCOUNTS?\s+USE\s+CODE:\s*([A-Z0-9]+)',
                r'FREE\s+\d+\s+STARTER.*?CODE:\s*([A-Z0-9]+)',
                r'STARTER.*?CODE:\s*([A-Z0-9]+)'
            ],
            'Starter Plus': [
                r'STARTER\s+PLUS\s+ACCOUNTS?\s+USE\s+CODE:\s*([A-Z0-9]+)',
                r'START\s+PLUS.*?CODE:\s*([A-Z0-9]+)',
                r'PLUS.*?CODE:\s*([A-Z0-9]+)'
            ],
            'Expert': [
                r'EXPERT\s+ACCOUNTS?\s+USE\s+CODE:\s*([A-Z0-9]+)',
                r'EXPERT.*?CODE:\s*([A-Z0-9]+)'
            ]
        }
    
    def setup_tesseract(self):
        """Setup Tesseract OCR with automatic path detection"""
        if not TESSERACT_AVAILABLE:
            self.available = False
            return
        
        try:
            # Set Tesseract path for Windows
            if platform.system() == "Windows":
                tesseract_paths = [
                    r"C:\Program Files\Tesseract-OCR\tesseract.exe",
                    r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
                    r"C:\Tesseract-OCR\tesseract.exe"
                ]
                
                for path in tesseract_paths:
                    if os.path.exists(path):
                        pytesseract.pytesseract.tesseract_cmd = path
                        break
            
            # Test Tesseract installation
            version = pytesseract.get_tesseract_version()
            self.available = True
            
        except Exception as e:
            self.available = False
    
    def is_available(self) -> bool:
        """Check if Tesseract is available"""
        return getattr(self, 'available', False)
    
    def extract_text_from_frame(self, frame: np.ndarray) -> List[str]:
        """Extract text from video frame using optimized Tesseract settings"""
        if not self.is_available():
            return []
        
        try:
            # Convert frame to PIL Image
            if len(frame.shape) == 3:
                pil_image = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
            else:
                pil_image = Image.fromarray(frame)
            
            # Use PSM 7 for single text line detection (best character recognition)
            # Avoid PSM 8 which causes 1/l confusion
            config = r'--oem 3 --psm 7'
            
            # Get detailed data with bounding boxes and confidence scores
            data = pytesseract.image_to_data(pil_image, config=config, output_type=pytesseract.Output.DICT)
            
            texts = []
            n_boxes = len(data['level'])
            
            for i in range(n_boxes):
                confidence = float(data['conf'][i])
                text = data['text'][i].strip()
                
                # Filter high confidence text (Tesseract confidence can be lower than EasyOCR)
                if confidence > 30 and text and len(text) > 1:
                    texts.append(text)
            
            return texts
            
        except Exception as e:
            return []
    
    def detect_codes_in_frame(self, frame: np.ndarray, enabled_account_types: List[str]) -> List[Tuple[str, str]]:
        """Detect codes in video frame for specified account types"""
        # Extract text from entire frame (not cropped)
        texts = self.extract_text_from_frame(frame)
        
        if not texts:
            return []
        
        # Combine all text into a single string for pattern matching
        combined_text = ' '.join(texts).upper()
        
        detected_codes = []
        
        # Search for codes using account type patterns
        for account_type in enabled_account_types:
            if account_type in self.account_patterns:
                for pattern in self.account_patterns[account_type]:
                    matches = re.finditer(pattern, combined_text, re.IGNORECASE)
                    for match in matches:
                        code = match.group(1)
                        if len(code) >= 4:  # Minimum code length
                            # Apply S/5 correction
                            corrected_code = self.correct_s5_confusion(code)
                            detected_codes.append((corrected_code, account_type))
        
        # Also look for generic CODE: patterns
        generic_pattern = r'CODE:\s*([A-Z0-9]+)'
        matches = re.finditer(generic_pattern, combined_text, re.IGNORECASE)
        for match in matches:
            code = match.group(1)
            if len(code) >= 4:
                # Try to determine account type from surrounding text
                account_type = self.determine_account_type_from_context(combined_text, enabled_account_types)
                if account_type:
                    corrected_code = self.correct_s5_confusion(code)
                    detected_codes.append((corrected_code, account_type))
        
        # Remove duplicates
        return list(set(detected_codes))
    
    def determine_account_type_from_context(self, text: str, enabled_types: List[str]) -> Optional[str]:
        """Determine account type from surrounding text context"""
        text_upper = text.upper()
        
        # Check for account type keywords
        type_keywords = {
            'Starter': ['STARTER', 'START'],
            'Starter Plus': ['STARTER PLUS', 'START PLUS', 'PLUS'],
            'Expert': ['EXPERT']
        }
        
        for account_type in enabled_types:
            if account_type in type_keywords:
                for keyword in type_keywords[account_type]:
                    if keyword in text_upper:
                        return account_type
        
        # Default to first enabled type if no specific match
        return enabled_types[0] if enabled_types else None
    
    def correct_s5_confusion(self, code: str) -> str:
        """Correct common S/5 OCR confusion in codes"""
        if not code:
            return code
        
        corrected = code
        
        # Apply context-based S/5 correction
        for i, char in enumerate(code):
            if char in ['S', '5']:
                # Get context around the character
                before = code[:i].upper()
                after = code[i+1:].upper()
                
                # Patterns that suggest '5'
                five_patterns = [
                    # Numbers before/after suggest this should be a number
                    bool(re.search(r'\d', before)) and bool(re.search(r'\d', after)),
                    # Common number sequences
                    char == 'S' and (before.endswith('1') or before.endswith('2') or before.endswith('3')),
                    # Position-based rules (5 more common at end of codes)
                    i > len(code) * 0.7,
                ]
                
                # Patterns that suggest 'S'
                s_patterns = [
                    # Beginning of code often starts with letters
                    i == 0,
                    # Common letter combinations
                    char == '5' and (after.startswith('T') or after.startswith('R')),
                    # Mixed alphanumeric codes often have S
                    bool(re.search(r'[A-Z]', before)) and bool(re.search(r'[A-Z]', after)),
                ]
                
                if any(five_patterns) and not any(s_patterns):
                    corrected = corrected[:i] + '5' + corrected[i+1:]
                elif any(s_patterns) and not any(five_patterns):
                    corrected = corrected[:i] + 'S' + corrected[i+1:]
        
        return corrected
    
    def preprocess_frame_for_ocr(self, frame: np.ndarray) -> np.ndarray:
        """Preprocess frame for better OCR accuracy"""
        try:
            # Convert to grayscale if needed
            if len(frame.shape) == 3:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            else:
                gray = frame.copy()
            
            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)
            
            # Apply adaptive thresholding for better text contrast
            thresh = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
            
            # Apply morphological operations to clean up the image
            kernel = np.ones((2, 2), np.uint8)
            cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            # Scale up the image for better OCR accuracy
            height, width = cleaned.shape
            if height < 100 or width < 400:
                scale_factor = max(2, 100 // height)
                scaled = cv2.resize(cleaned, (width * scale_factor, height * scale_factor), 
                                  interpolation=cv2.INTER_CUBIC)
                return scaled
            
            return cleaned
            
        except Exception as e:
            return frame

# Global instance for easy access
console_ocr = ConsoleTesseractOCR()

def get_ocr_engine() -> ConsoleTesseractOCR:
    """Get the console OCR engine instance"""
    return console_ocr

def is_ocr_available() -> bool:
    """Check if OCR is available"""
    return console_ocr.is_available()

def extract_codes_from_frame(frame: np.ndarray, account_types: List[str]) -> List[Tuple[str, str]]:
    """Extract codes from frame - convenience function"""
    return console_ocr.detect_codes_in_frame(frame, account_types)
