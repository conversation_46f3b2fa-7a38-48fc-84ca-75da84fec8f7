# MFFUHijack Console Scanner

A clean, console-only version of MFFUHijack with Tesseract OCR exclusively. Features a menu-driven interface for real-time and test scanning of livestream codes.

## Features

### 🎯 **Core Functionality**
- **Tesseract OCR Only**: Optimized single-engine implementation
- **Full Frame Scanning**: Scans entire video frame (not just bottom portion)
- **PSM 7 Configuration**: Best character recognition, avoids 1/l confusion
- **Clean Console Interface**: No persistent logging, clear menu navigation
- **Persistent History**: Stores all detected codes with timestamps

### 📺 **Scanning Modes**

#### Real-Time Scanner
- YouTube livestream URL input
- Multi-select account types (Starter, Starter Plus, Expert)
- Account credentials storage
- Live scanning dashboard with real-time stats

#### Test Scanner
- File selection from `test_streams` directory
- Configurable scan intervals (default: 5 seconds)
- Start time specification (HH:MM:SS format)
- Video file processing with frame-by-frame analysis

### 🎨 **Interface Design**
- ASCII banner branding
- Numbered menu navigation
- Console clearing between states
- Real-time dashboard updates
- Clean, structured displays

## Installation

### Prerequisites
```bash
# Python packages
pip install pytesseract opencv-python pillow numpy

# Tesseract binary
# Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki
# macOS: brew install tesseract
# Linux: sudo apt install tesseract-ocr
```

### Verification
```bash
python test_console_app.py
```

## Usage

### Launch Application
```bash
python mffuhijack_console.py
```

### Main Menu Options
1. **Real-Time Scanner** - Scan live YouTube streams
2. **Test Scanner** - Test with downloaded video files
3. **Exit** - Close application

### Real-Time Scanner Setup
1. Enter YouTube livestream URL
2. Select account types to scan for
3. Enter account credentials
4. Start scanning or view history

### Test Scanner Setup
1. Select existing video file or download new stream
2. Configure scan interval and start time
3. Select account types to scan for
4. Enter account credentials
5. Start scanning or view history

### Scanning Interface
- **Clean Dashboard**: Shows current time, frames processed, codes detected
- **Real-Time Updates**: Live statistics without scrolling text
- **Detection Alerts**: Timestamped code discoveries
- **Account Type Breakdown**: Counts by Starter/Starter Plus/Expert
- **Ctrl+C**: Stop scanning and return to menu

## Technical Details

### OCR Configuration
- **Engine**: Tesseract OCR exclusively
- **PSM Mode**: 7 (single text line) for optimal character recognition
- **Preprocessing**: Adaptive thresholding, noise reduction, scaling
- **S/5 Correction**: Intelligent character confusion correction

### Code Detection Patterns
```regex
Starter: STARTER\s+ACCOUNTS?\s+USE\s+CODE:\s*([A-Z0-9]+)
Starter Plus: STARTER\s+PLUS\s+ACCOUNTS?\s+USE\s+CODE:\s*([A-Z0-9]+)
Expert: EXPERT\s+ACCOUNTS?\s+USE\s+CODE:\s*([A-Z0-9]+)
Generic: CODE:\s*([A-Z0-9]+)
```

### File Structure
```
mffuhijack_console.py           # Main console application
MFFUHijack_Support/
├── console_ocr.py              # Tesseract OCR implementation
test_streams/                   # Video files for testing
scan_history.json               # Persistent detection history
test_console_app.py             # Verification script
```

### Performance Optimizations
- **Single OCR Engine**: Eliminates engine switching overhead
- **Frame Skipping**: Configurable intervals for efficient processing
- **Memory Management**: Proper video capture cleanup
- **Error Handling**: Robust exception handling without crashes

## Configuration

### Account Type Patterns
Customize detection patterns in `console_ocr.py`:
```python
self.account_patterns = {
    'Starter': [
        r'STARTER\s+ACCOUNTS?\s+USE\s+CODE:\s*([A-Z0-9]+)',
        r'FREE\s+\d+\s+STARTER.*?CODE:\s*([A-Z0-9]+)',
    ],
    # Add custom patterns here
}
```

### Tesseract Settings
Modify OCR configuration in `console_ocr.py`:
```python
config = r'--oem 3 --psm 7'  # Change PSM mode if needed
```

## Troubleshooting

### Common Issues

#### "Tesseract OCR not available"
- Install Tesseract binary for your OS
- Ensure it's in your system PATH
- Windows: Add `C:\Program Files\Tesseract-OCR` to PATH

#### "Video processing not available"
- Install OpenCV: `pip install opencv-python`
- Verify with: `python -c "import cv2; print(cv2.__version__)"`

#### "No codes detected"
- Check video quality and text visibility
- Verify account type selection matches stream content
- Test with known working video files

#### Console display issues
- Use terminal with proper Unicode support
- Ensure console window is wide enough (80+ characters)
- Try different terminal applications if needed

### Debug Mode
Run test script for detailed diagnostics:
```bash
python test_console_app.py
```

## Differences from GUI Version

### Removed Components
- ❌ EasyOCR engine support
- ❌ PaddleOCR engine support
- ❌ GUI components (PyQt6)
- ❌ Browser automation
- ❌ Live preview windows
- ❌ Settings persistence

### Enhanced Features
- ✅ Cleaner, faster interface
- ✅ Optimized single-engine performance
- ✅ Better console output management
- ✅ Simplified configuration
- ✅ Reduced memory footprint

## Development

### Adding New Account Types
1. Update `account_patterns` in `console_ocr.py`
2. Add to account type lists in menu functions
3. Test with sample video content

### Extending Functionality
- Real-time livestream integration (yt-dlp)
- Browser automation for code validation
- Advanced preprocessing options
- Custom detection algorithms

## License

Copyright (c) 2025 jordan. All rights reserved.

---

**MFFUHijack Console Scanner v1.0**  
*Tesseract OCR Engine - Clean Menu Interface*
