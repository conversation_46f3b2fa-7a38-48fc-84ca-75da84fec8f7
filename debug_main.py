#!/usr/bin/env python3
"""
Debug script to test main function
"""

import os
import sys

print("=== TESTING MAIN FUNCTION ===")

# Add support folder to path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'MFFUHijack_Support'))

try:
    from mffuhijack_console import ConsoleInterface, HistoryManager, TesseractScanner, Colors, OCR_AVAILABLE
    print("1. All imports successful")
    
    console = ConsoleInterface()
    history = HistoryManager()
    scanner = TesseractScanner()
    
    print("2. All objects created")
    
    print(f"3. OCR Available: {OCR_AVAILABLE}")
    print(f"4. Scanner available: {scanner.is_available()}")
    
    print("5. Testing menu display...")
    console.show_main_menu()
    print("6. Menu displayed successfully")
    
    print("7. Testing input prompt...")
    # Don't actually wait for input, just test the setup
    
except Exception as e:
    print(f"Error in main function test: {e}")
    import traceback
    traceback.print_exc()

print("=== MAIN FUNCTION TEST COMPLETE ===")
