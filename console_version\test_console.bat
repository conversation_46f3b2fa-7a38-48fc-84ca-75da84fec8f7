@echo off
title MFFUHijack Console Scanner - Test
color 0E

echo.
echo ===============================================================================
echo                    MFFUHIJACK CONSOLE SCANNER - TEST SUITE
echo ===============================================================================
echo.
echo Running comprehensive tests to verify all components are working...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    echo.
    pause
    exit /b 1
)

REM Check if test file exists
if not exist "test_console_app.py" (
    echo ERROR: test_console_app.py not found
    echo Please ensure all files are in the console_version directory
    echo.
    pause
    exit /b 1
)

REM Run the test suite
python test_console_app.py

echo.
echo ===============================================================================
echo Test suite completed. Check results above.
echo ===============================================================================
echo.
pause
