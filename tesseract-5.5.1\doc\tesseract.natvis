<?xml version="1.0" encoding="utf-8"?>
<AutoVisualizer xmlns="http://schemas.microsoft.com/vstudio/debugger/natvis/2010">
  <Type Name="GenericVector&lt;*&gt;">
    <DisplayString>{{size={size_used_}}}</DisplayString>
    <Expand>
      <Item Name="[size]" ExcludeView="simple">size_used_</Item>
      <Item Name="[capacity]" ExcludeView="simple">size_reserved_</Item>
      <ArrayItems>
        <Size>size_used_</Size>
        <ValuePointer>data_</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>

  <Type Name="tesseract::IntParam">
    <DisplayString>{value_}</DisplayString>
  </Type>
  <Type Name="tesseract::BoolParam">
    <DisplayString>{value_}</DisplayString>
  </Type>

  <Type Name="tesseract::StringParam">
    <DisplayString>{value_}</DisplayString>
  </Type>

  <Type Name="tesseract::DoubleParam">
    <DisplayString>{value_}</DisplayString>
  </Type>

</AutoVisualizer>
